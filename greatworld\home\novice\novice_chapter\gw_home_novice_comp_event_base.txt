---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by hans<PERSON><PERSON>.
--- DateTime: 2024/9/2 14:55
--- Des: 事件基类

local require = require
local tostring = tostring
local pairs = pairs
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local gw_main_mini_game_data = require "gw_main_mini_game_data"
local event = require "event"
local gw_home_effect_mgr = require "gw_home_effect_mgr"
local gw_ed = require "gw_ed"
local util = require "util"
local ui_pointing_target = require "ui_pointing_target"
local lang = require "lang"
local puzzlegame_mgr = require "puzzlegame_mgr"
local flow_text = require "flow_text"
local const = require "const"
local net_city_module = require "net_city_module"
local ui_window_mgr = require "ui_window_mgr"
local unit_base_object = require "unit_base_object"
local game_scheme = require "game_scheme"
local GWG = GWG
local GWConst = require "gw_const"
local gw_home_grid_data = require "gw_home_grid_data"
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWNoviceEventBase : unit_base 事件基类
module("gw_home_novice_comp_event_base")
local GWEventObject = newclass("gw_home_novice_comp_event_base", unit_base_object)

--- 构造器
function GWEventObject:ctor()
    self:ClearData()
    self.refreshUpgrade = function()
        self:IsCurrentEvent()
    end
    unit_base_object.ctor(self)
end

--- @public 设置数据 （现必须基成设置数据）
---@see override
function GWEventObject:InitData(cityMapId, eventId)
    self.mapId = cityMapId
    self.eventId = eventId
    self:InitCfgData(cityMapId, eventId)
    self:InitLoadRes()
    self:InstantiateModelAsync(self.res, GWG.GWHomeNode.eventNode())
end

--- @public 初始化配置数据
function GWEventObject:InitCfgData(cityMapId, eventId)
    local mapCfg = game_scheme:BuildMaincityMap_0(cityMapId)
    local eventCfg = game_scheme:BuildPreProcess_0(eventId)

    if not mapCfg then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject mapCfg = nil", cityMapId)
        return false
    end

    if not eventCfg then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg = nil", eventId)
        return false
    end

    self.mapCfg = mapCfg
    self.eventCfg = eventCfg
    return true
end

function GWEventObject:InitLoadRes()
    self.res = "art/greatworld/home/<USER>/novice/homenoviceeventbase.prefab"
    self.angele = 0
end

--- @public 基类方法实例化模型
---@see override
function GWEventObject:InstantiateModelAsync(path, parent)
    unit_base_object.InstantiateModelAsync(self, path, parent)
end

--- 实例化成功 （现必须基成设置名字和组件Id）
---@see override
function GWEventObject:InstantiateSuccess(_obj)
    _obj.tag = GWG.GWConst.EHomeBuildEntityTagType.HomeBuildEvent
    self.defaultY = GWG.GWConst.HomeMapDefaultY

    unit_base_object.InstantiateSuccess(self, _obj)
    self.sortingGroup = UIUtil.GetComponent(_obj.transform, "SortingGroup")
    self.node = UIUtil.GetTrans(self.transform, "node")
    self.effect = UIUtil.GetTrans(self.node, "HomeEventEffect")
    local box = self.node:GetChild(0)
    local platform = self.node:GetChild(2)
    self.ground = UIUtil.GetComponent(self.node, "Transform", "ground")
    if box then
        box.name = "build_" .. self.compId
    end
    UIUtil.SetRotation(self.ground, 90, 0, self.angele)
    self.size = self.eventCfg.tile
    self:SetGridPos(self.mapCfg.x, self.mapCfg.y)
    self:SetScale(2, true)
    self:IsCurrentEvent()
    if util.IsObjNull(platform) then
        return
    end
    UIUtil.SetActive(platform, self.eventId < 7)
end

function GWEventObject:IsEventXyx()
    if self.isInitXyx then
        return self.isXyx
    end
    self.isInitXyx = true
    self.isXyx = false
    local laymain_data = require "laymain_data"
    local gw_ab_test_mgr = require "gw_ab_test_mgr"
    local mapCfg = laymain_data.GetHookLevelByID(self.eventCfg.TypePara.data[0])
    --根据游戏类型判断
    local buildType = gw_ab_test_mgr.GetHookLevelType()
    if buildType == 3 and mapCfg.miniGameType and mapCfg.miniGameID and mapCfg.miniGameID > 0 then
        local mixedType = gw_ab_test_mgr.GetHookLevelMixedType()
        local miniGameType = nil
        local miniGameID = nil
        if mixedType == 1 then
            miniGameType = mapCfg["miniGameType"]
            miniGameID = mapCfg["miniGameID"]
        else
            miniGameType = mapCfg["miniGameType" .. mixedType]
            miniGameID = mapCfg["miniGameID" .. mixedType]
        end
        if miniGameType == 2 then
            self.isXyx = true
            --self.isXyx = false
        end
    end
end

---@public 设置格子
function GWEventObject:SetGridPos(x, y)
    self:SetLayer(x, y)
    self:ClearGridPos(x, y)
    --gw_home_grid_data.SetGridStateByBuilding(x, y, self.compId, self.size)
    self.curGridX = x
    self.curGridY = y
    local x1, y1, z1 = gw_home_grid_data.GetPosByGridXY(x, y)
    self:SetPosition(x1, y1, z1)
end

---@public 清楚格子坐标
function GWEventObject:ClearGridPos(x, y)
    if self.curGridX and (self.curGridX ~= x or self.curGridY ~= y) then
        --先清掉原来的数据
        --gw_home_grid_data.SetGridStateByBuilding(self.curGridX, self.curGridY, 0, self.size)
    end
end

function GWEventObject:RegisterListener()
    gw_ed.mgr:Register(gw_ed.GW_HOME_BUILDING_UPGRADE, self.refreshUpgrade)
    unit_base_object.RegisterListener(self)
end

function GWEventObject:UnregisterListener()
    if self.refreshUpgrade then
        gw_ed.mgr:Unregister(gw_ed.GW_HOME_BUILDING_UPGRADE, self.refreshUpgrade)
    end
    unit_base_object.UnregisterListener(self)
end

function GWEventObject:NoviceLimitClick(isClick)
    self.isLimitClick = isClick
end

---@public 点击
function GWEventObject:OnClickGrid(names, gridX, gridY)
    if self.isLimitClick then
        return
    end
    if self.isLoaded and not GWG.GWHomeMgr.noviceChapterData.GetMoveActionIng() then
        if not self.eventCfg then
            GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg = nil", self.eventId)
            return
        end
        if not self.eventCfg.PreEvent then
            GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg.PreEvent = nil", self.eventId)
            return
        end
        local curEventId = GWG.GWHomeMgr.noviceChapterData.GetCurrentEventPoint()
        self:StartEvent()
        local curCompleteEventCfg = game_scheme:BuildPreProcess_0(curEventId)
        if not curCompleteEventCfg then
            return
        end
        local playerEventId = GWG.GWHomeMgr.noviceChapterData.GetPlayerEventId()
        if self.eventCfg.EventID == curCompleteEventCfg.PreEvent and self.eventCfg.EventID ~= playerEventId then
            GWG.GWHomeMgr.noviceChapterData.RefreshUpdateCheckEvent()
        end
    end
end
---@public 主线关卡点击事件
local function OnBtn_FightClicked(level)
    local equipment_mgr = require "equipment_mgr"
    if equipment_mgr.CheckEquipIsFull() == true then
        equipment_mgr.ShowEquipFullTip()
        return false
    end
    local hangLevel = level
    local battle_switch_manager = require "battle_switch_manager"
    local common_new_pb = require "common_new_pb"
    local isBattling = battle_switch_manager.IsBattling(common_new_pb.GameGoal)
    local mapCfg = game_scheme:HookLevel_0(hangLevel)
    if const.OPEN_NEW_HOOK_SCENE then
        local new_hook_scene = require("new_hook_scene")
        new_hook_scene.OnFightClicked(level)
    else
        local laymain_top_scene = require("laymain_top_scene")
        laymain_top_scene.OnFightClicked(level)
    end
end
---@public 小游戏关卡点击事件
local function OnBtn_miniGameClicked(level)
    --local minilevelCfg = puzzlegame_mgr.GetLevelIdTinyGameLevelInfo(level)
    --local minilevelCfg = game_scheme:MiniGameLevelControl_1(ABTestManager.GetMiniGameUnlockType(), puzzlegame_mgr.getMiniGameType(), level)
    --local minilevelCfg = puzzlegame_mgr.GetTinyGameLevelInfo(level)
    --[[if not minilevelCfg then
        GWG.GWAdmin.SwitchUtility.Error("OnBtn_miniGameClicked minilevelCfg = nil", level)
        return
    end
    --处理点击关卡章节页面的引导特效
    if minilevelCfg.iPreLevel == 0 or puzzlegame_mgr.getIsFinishByLevelId(minilevelCfg.iPreLevel, minilevelCfg.LevelType) then
        if ui_window_mgr:IsModuleShown("ui_pointing_target") then
            ui_pointing_target.CloseWithParam()
        end
        puzzlegame_mgr.OpenMiniGame(minilevelCfg.ID)
    else
        flow_text.Add(lang.Get(2718))
    end]]
end
---@public 奖励点击事件
local function OnBtn_RewardClicked(eventId)
    if not eventId then
        GWG.GWAdmin.SwitchUtility.Error("OnBtn_RewardClicked eventId = nil")
        return
    end
    net_city_module.MSG_CITY_GET_EVENT_REWARD_REQ(eventId)
end
---@public 判断事件解锁条件是否满足
---@param eventId number 事件id
function GWEventObject:GetBuildEventUnlock()
    if not self.eventCfg then
        return
    end
    if not self.eventCfg.unlock then
        return
    end
    if self.eventCfg.unlock.count <= 0 then
        return true
    end
    local unlockData = self.eventCfg.unlock.data
    local len = util.get_len(unlockData)
    if not unlockData or len < 2 then
        return handleError("eventCfg.unlock.data is nil or 数据错误")
    end
    local needBuildingId = unlockData[0]
    local needBuildLevel = unlockData[1]
    local nLevel = GWG.GWHomeMgr.buildingData.GetBuildingDataMaxLevel(needBuildingId)
    if nLevel >= needBuildLevel then
        return true
    end
    return false, needBuildingId, needBuildLevel
end
--- @public 开始执行事件
function GWEventObject:StartEvent()
    if not self:CheckEventConfig() then
        return
    end
    ui_window_mgr:UnloadModule("ui_bs_operate")
    local isUnlock, needId, needLevel = self:GetBuildEventUnlock()
    if not isUnlock then
        GWG.GWAdmin.HomeCameraUtil.DoCameraToBuildMove(needId, needLevel, 100, false, nil, nil, nil)
        return
    end
    local curEventId = GWG.GWHomeMgr.noviceChapterData.GetCurrentEventPoint()
    local function moveCallBack()
        if self.eventCfg.type == GWConst.BuildEventType.MainQuest then
            OnBtn_FightClicked()
            ui_window_mgr:UnloadModule("ui_bs_operate")
        elseif self.eventCfg.type == GWConst.BuildEventType.MiniGame then
            GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg.type = nil", self.eventCfg.type)
            --OnBtn_miniGameClicked(self.eventCfg.TypePara.data[0])
        else
            GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg.type = nil", self.eventCfg.type)
        end
    end

    local callBack = function() 
        if self.eventId ~= curEventId then
            --跳转到对应事件
            local mapId = GWG.GWHomeMgr.noviceChapterData.GetMainCityMapId(curEventId)
            local mapCfg = game_scheme:BuildMaincityMap_0(mapId)
            if not mapCfg then
                return
            end
            --moveCallBack()
            GWG.GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(mapCfg.x, mapCfg.y,1, false, moveCallBack )
        else
            if self.eventCfg then
                event.EventReport("NewbieEvent_Start", { EventID = self.eventId})
            end
            GWG.GWHomeMgr.noviceChapterData.NextLevelMove(nil, curEventId, moveCallBack)
        end 
    end

    --事件4，老号城门可能为0级，需要直接修复城门
    if curEventId == 4 then
        local gw_home_common_util = require "gw_home_common_util"
        local gw_jump_util = require "gw_jump_util"
        local isRepaire = gw_home_common_util.GetBuildDataIsRepairByMapCityId(80)
        local isCut = gw_home_common_util.GetBuildDataIsCutByMapCityId(80)
        
        if isRepaire or isCut then
            local buildType = GWConst.enBuildingType.enBuildingType_Wall
            local str = tostring(buildType).."#".."0"
            gw_jump_util.JumpToBuildAndMenu(str)
        else
            callBack()
        end
    else
        callBack()
    end
end

--- @public 模型出现
function GWEventObject:ShowModel(callback)
    if self.eventCfg.type == GWConst.BuildEventType.StartPoint then
        return
    end
    local playEffect = false
    if not self.modelComp then
        self:SetEventModel()
        playEffect = true
    end
    self.modelComp:SetInstantiateSuccessCallback(callback)
    self.modelComp:ShowModel(playEffect)
end

--- @public 模型死亡
function GWEventObject:DeadModel(callBack)
    self:RemoveNoviceHud()
    if self.eventCfg.type == GWConst.BuildEventType.StartPoint then
        return
    end
    self.deadModeAction = true --正在执行死亡动画
    if not self.modelComp then
        self:SetEventModel()
    end
    if self.modelComp then
        self.modelComp:DeadModel(callBack, function()
            self:DestroyModel()
        end)
    end
    self:StopEffect(GWConst.HomeEffectType.EventCombat)
    self:DisposeFightHud()
end

--- @public 模型销毁
function GWEventObject:DestroyModel()
    self:RemoveNoviceHud()
    self:DisposeFightHud()
    if self.modelComp then
        GWG.GWAdmin.PushBSComponent(self.modelComp)
        self.modelComp = nil
    end
end

function GWEventObject:SetModelVisible(show)
    if self.modelComp then
        self.modelComp:SetVisible(show)
    end
end

function GWEventObject:GetModelComp()
    return self.modelComp
end

function GWEventObject:Hide()
    self:RemoveNoviceHud()
    self:DisposeFightHud()
    if util.IsObjNull(self.gameObject) then
        return
    end
    self.gameObject:SetActive(false)
end
function GWEventObject:Show()
    if util.IsObjNull(self.gameObject) then
        return
    end
    self:CreateFightHud()
    self.gameObject:SetActive(true)
end

--- @public 设置模型
function GWEventObject:SetEventModel()
    if self.modelComp then
        return
    end
    if self.eventCfg and self.eventCfg.TypePara then
        local id = GWG.GWIdMgr:AllocComponentId()
        self.modelComp = GWG.GWAdmin.PopBSComponent(GWG.GWCompName.gw_home_novice_comp_event_model, id)
        if self.modelComp then
            self.modelComp:InitData(self.eventId, self.mapId, GWG.GWHomeNode.eventNode(), self.eventCfg.ResPath)
        else
            GWG.GWAdmin.SwitchUtility.Error("SetEventModel self.modelComp =  nil")
        end
    end
end

---@public 是否是当前关卡 显示高亮
function GWEventObject:IsCurrentEvent(isHideEffect)
    self:DisposeEffect()
    self:DisposeFightHud()
    local curEventId = GWG.GWHomeMgr.noviceChapterData.GetCurrentEventPoint()
    if self.eventId < curEventId then
        if not self.deadModeAction then
            self:DestroyModel()
        end
        self:Hide()
        return
    end
    local curDistance = self.eventId - curEventId + 1
    local isCreateBubbleType = self.eventCfg.type == GWConst.BuildEventType.MainQuest or
            self.eventCfg.type == GWConst.BuildEventType.MiniGame
    if isCreateBubbleType and self:IsEventXyx() then
        --创建战斗Hud
        self:CreateFightHud()
    end
    if self.eventId == curEventId then
        local isUnlock, needId, needLevel = self:GetBuildEventUnlock()
        if isUnlock then
            --显示高亮
            self:ShowIsCurrentEffect()
            --不是奖励类型
            if isCreateBubbleType and not self:IsEventXyx() then
                --创建战斗Hud
                self:CreateFightHud()
            end
        end
    end
    if self.eventCfg.display <= 0 --[[or self.eventCfg.display >= curDistance]] then
        -- 显示模型
        self:ShowModel()
    else
        self:Hide()
    end
end

function GWEventObject:RefreshData(isHideEffect)
    self:AddLoadEvent(function()
        self:IsCurrentEvent(isHideEffect)
    end)
end
function GWEventObject:ShowIsCurrentEffect()
    if self.eventCfg.type == GWConst.BuildEventType.Reward then
        self:PlayEffect(GWConst.HomeEffectType.EventRewardGrid)
    else
        self:PlayEffect(GWConst.HomeEffectType.EventCombatGrid)
    end
end
function GWEventObject:PlayBoxOpenEffect()
    self:PlayEffect(GWConst.HomeEffectType.EventBoxOpen)
end
function GWEventObject:StopBoxOpenEffect()
    self:StopEffect(GWConst.HomeEffectType.EventBoxOpen)
end
---@public 创建战斗Hud
function GWEventObject:CreateFightHud()
    local scale = self.eventId >= 7 and 2.2 or 1.7
    scale = 1.7
    local isXyx = self:IsEventXyx()
    if isXyx then
        local clickFunc = function()
            self:OnClickGrid()
        end
        self.fightBubble = GWG.GWHomeMgr.bubbleMgr.BindBubbleEntity(nil, GWConst.EHomeBubbleEntityType.XYXEvent, self.transform, clickFunc, nil);
        local data = { offsetData = { x = 0, y = (self.eventCfg.ResScale + 0.2) * 50 + 10 } }
        if self.fightBubble then
            GWG.GWHomeMgr.bubbleMgr.BubbleUpdateEntity(self.fightBubble, data)
        end
    else
        local compName = GWG.GWCompName.gw_home_comp_hud_fight
        if not self:GetComponent(compName) then
            local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
            comp:SetOffsetScale(scale)
            comp:SetOnClick(function()
                self:OnClickGrid()
            end)
            self:AddComponent(compName, comp)
        end
    end
end
function GWEventObject:GetFightHud()
    local isXyx = self:IsEventXyx()
    if isXyx then
        return GWG.GWHomeMgr.bubbleMgr.GetBubbleEntity(self.fightBubble)
    else
        local compName = GWG.GWCompName.gw_home_comp_hud_fight
        return self:GetComponent(compName)
    end
end
function GWEventObject:DisposeFightHud()
    if not self.eventCfg then
        local compName = GWG.GWCompName.gw_home_comp_hud_fight
        self:RemoveComponent(compName)
        return
    end
    local isXyx = self:IsEventXyx()
    if isXyx then
        if self.fightBubble then
            GWG.GWHomeMgr.bubbleMgr.DisposeBubbleEntity(self.fightBubble)
            self.fightBubble = nil
        end
    else
        local compName = GWG.GWCompName.gw_home_comp_hud_fight
        self:RemoveComponent(compName)
    end
end

function GWEventObject:CreateNoviceHud()
    self:AddLoadEvent(function()
        local compName = GWG.GWCompName.gw_home_comp_hud_novice
        if not self:GetComponent(compName) then
            local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
            self:AddComponent(compName, comp)
        end
    end)
end
function GWEventObject:RemoveNoviceHud()
    local compName = GWG.GWCompName.gw_home_comp_hud_novice
    self:RemoveComponent(compName)
end

function GWEventObject:PlayEffect(type, pos)
    if not self.effectGroup then
        self.effectGroup = {}
    end
    if not self.effectGroup[type] then
        local compId = gw_home_effect_mgr.CreateEffects(type, self.transform, pos)
        self.effectGroup[type] = compId
    end
end
function GWEventObject:StopEffect(type)
    if self.effectGroup and self.effectGroup[type] then
        gw_home_effect_mgr.RemoveEffect(self.effectGroup[type])
        self.effectGroup[type] = nil
    end
end

function GWEventObject:DisposeEffect()
    if self.effectGroup then
        for i, effectId in pairs(self.effectGroup) do
            gw_home_effect_mgr.RemoveEffect(effectId)
        end
        self.effectGroup = {}
    end
end

function GWEventObject:ShowModelAbilityTrigger()
    if self.modelComp then
        self.modelComp:ShowAbilityTrigger()
    end
end
function GWEventObject:ShowModelStandTrigger()
    if self.modelComp then
        self.modelComp:ShowStandTrigger()
    end
end

function GWEventObject:RunFunction(funcName, ...)
    if funcName and self.modelComp[funcName] then
        self.modelComp[funcName](self.modelComp, ...)
    end
end

---清楚数据
---@see override
function GWEventObject:ClearData()
    if self.fightBubble then
        GWG.GWHomeMgr.bubbleMgr.DisposeBubbleEntity(self.fightBubble)
        self.fightBubble = nil
    end
    self:ClearGridPos()
    self:DestroyModel()
    self:DisposeEffect()
    self.curGridX = nil
    self.curGridY = nil
    self.mapId = nil
    self.eventId = nil
    self.size = nil
    self.sortingGroup = nil
    self.node = nil
    self.effect = nil
    self.ground = nil
    self.deadModeAction = nil
    self.playerEffect = nil
    self.currentEffect = nil
    self.rewardEffect = nil
    self.fightEffect = nil
    self.isLimitClick = nil
    self.isInitXyx = nil
    self.isXyx = nil
end
function GWEventObject:SetLayer(gridX, gridY)
    if gridX == nil or gridY == nil then
        gridX = self.serData.x
        gridY = self.serData.y
    end
    --设置对应的层级
    if not util.IsObjNull(self.sortingGroup) then
        self.sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(gridX, gridY)
    end
end
function GWEventObject:CheckEventConfig()
    if not self.eventCfg or not self.eventCfg.type then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg = nil", self.eventId)
        return false
    end
    return true
end

--- 弃置
---@see override
function GWEventObject:Dispose()
    self:ClearData()
    unit_base_object.Dispose(self)
end

--- 重置为了循环利用
---@see override
function GWEventObject:Recycle()
    self:ClearData()
    unit_base_object.Recycle(self)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWEventObject