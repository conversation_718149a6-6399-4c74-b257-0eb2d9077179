---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/8/22 20:15
--- Desc: 气泡联盟帮助类

local require = require
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local laymain_data = require "laymain_data"
local player_mgr = require "player_mgr"
local game_scheme = require "game_scheme"
local util = require "util"
local tostring = tostring
local gw_straydog_mgr = require "gw_straydog_mgr"
local gw_home_comp_build_bubble_base = require "gw_home_comp_build_bubble_base"
module("gw_home_comp_bubble_doghouse")
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class gw_home_comp_bubble_doghouse : GWDisposableObject
local GWHomeCompBubbleDogHouse = newclass("gw_home_comp_bubble_doghouse", gw_home_comp_build_bubble_base)

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

function GWHomeCompBubbleDogHouse:InitData(buildData, bindParent, parent, bubbleType)
    gw_home_comp_build_bubble_base.InitData(self, buildData, bindParent, bubbleType)
    self:InstantiateModelAsync("ui/prefabs/gw/buildsystem/bubbleui/bubbledoghouseui.prefab", parent);
end

function GWHomeCompBubbleDogHouse:InstantiateSuccess(obj)
    self.rewardText = UIUtil.GetComponent(obj.transform, "Text", "Text");
    gw_home_comp_build_bubble_base.InstantiateSuccess(self, obj)
end

---@public 检测气泡
---@see override
function GWHomeCompBubbleDogHouse:RefreshCheckState()
    gw_home_comp_build_bubble_base.RefreshCheckState(self)
    if self.rewardText then
        self.rewardText.text = tostring(gw_straydog_mgr.GetRewardRedCount())
    end
end

return GWHomeCompBubbleDogHouse
