local GWG = GWG
local require = require
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local table = table
local GWConst = require "gw_const"
local util = require "util"
local lang = require "lang"
local print = print
local gw_home_comp_hud_base = require "gw_home_comp_hud_base"
local typeof = typeof
local gw_home_config_mgr = require "gw_home_config_mgr"
local gw_home_card_sprite_asset_mgr = require "gw_home_card_sprite_asset_mgr"
local Animator = CS.UnityEngine.Animator
local val = require("val")
local isPerf = val.IsTrue("sw_home_bubble_new", 0)

-- 添加Unity类型引用
local Camera = CS.UnityEngine.Camera
local RenderMode = CS.UnityEngine.RenderMode
local Vector3 = CS.UnityEngine.Vector3
local Quaternion = CS.UnityEngine.Quaternion
local Time   = CS.UnityEngine.Time
local ipairs = ipairs
local math = math
local GWG = GWG

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWHUDStrayDogBubble : unit_base 流浪狗气泡HUD对象
module("gw_home_comp_hud_stray_dog_bubble")
local GWHUDStrayDogBubbleObject = newclass("gw_home_comp_hud_stray_dog_bubble", gw_home_comp_hud_base)

local XOffset = 89
local yOffset = 60


--- 构造器
function GWHUDStrayDogBubbleObject:ctor()
    gw_home_comp_hud_base.ctor(self)

    -- 图标队列管理（替代文本队列）
    self.usedIcons = {}        -- 已使用的图标队列
    self.availableIcons = {}   -- 可用的图标队列
    self.currentIconSet = nil  -- 当前使用的图标集合

    -- 时间管理
    self.lastTriggerTime = 0   -- 上次触发时间
    self.hideTimer = nil       -- 隐藏计时器
    self.isShowing = false     -- 是否正在显示

    -- 精灵资源管理
    self.spriteAsset = nil     -- 气泡精灵资源

    -- 可用的表情图标列表
    self.emotionIcons = {
        "lzxl_icon_bq_keai",
        "lzxl_icon_bq_meng"
        -- 更多图标将来会添加
    }
end

---@public 设置数据
---@param bindParent table Transform 绑定的父节点
---@see override
function GWHUDStrayDogBubbleObject:InitData(bindParent, data)
    gw_home_comp_hud_base.InitData(self, bindParent, data)

    -- 保存流浪狗Transform引用，用于跟随
    if data and data.strayDogTransform then
        self.strayDogTransform = data.strayDogTransform
    end

    -- 初始化精灵资源
    if not self.spriteAsset then
        self.spriteAsset = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("gwbubble")
    end

    self:InstantiateModelAsync("art/greatworld/home/<USER>/hud/gwhomestraydogbubblehud.prefab", data.parent)
end

--- @public 基类方法实例化模型
---@param path string 模型路径
---@param parent table Transform 父节点
---@see override
function GWHUDStrayDogBubbleObject:InstantiateModelAsync(path, parent)
    gw_home_comp_hud_base.InstantiateModelAsync(self, path, parent)
end

---实例化成功
---@param _obj table GameObject 模型对象
---@see override
function GWHUDStrayDogBubbleObject:InstantiateSuccess(_obj)
    self.parentTr = UIUtil.GetComponent(_obj.transform, "RectTransform", "parent")
    self.headCanvas = UIUtil.GetComponent(_obj.transform, "Canvas")
    self.iconImg = UIUtil.GetComponent(_obj.transform, "Image", "parent/Icon")
    self.animators = UIUtil.GetComponent(_obj.transform, typeof(Animator), "")

    gw_home_comp_hud_base.InstantiateSuccess(self, _obj)

    -- 设置独立跟随模式
    self:SetupIndependentFollow()

    -- 初始化时强制隐藏气泡
    self.transform.gameObject:SetActive(false)

    -- 确保图标显示，隐藏文本相关组件
    self:RefreshIconLayout(true)

    self.order = 2000
    self:SetCanvasOrder()

end

-- 播放动画
function GWHUDStrayDogBubbleObject:PlayAnim(animName)
    if util.IsObjNull(self.animators) then
        return
    end
    self.animators:Play(animName)
end

-- 设置图标（替代设置文本）
function GWHUDStrayDogBubbleObject:SetIcon(iconName)
    self:AddLoadEvent(function()
        -- 确保图标显示，隐藏文本相关组件
        self:RefreshIconLayout(true)

        -- 使用精灵资源加载图标
        if self.spriteAsset and not util.IsObjNull(self.iconImg) then
            self.spriteAsset:GetSprite(iconName, function(sprite)
                if not util.IsObjNull(self.iconImg) and not util.IsObjNull(sprite) then
                    self.iconImg.sprite = sprite
                    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡图标设置成功: " .. iconName)
                else
                    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡图标设置失败: " .. iconName)
                end
            end)
        end
    end)
end

-- 刷新图标布局（显示图标，隐藏文本）
function GWHUDStrayDogBubbleObject:RefreshIconLayout(showIcon)
    -- 确保图标组件显示
    if not util.IsObjNull(self.iconImg) then
        UIUtil.SetActive(self.iconImg.gameObject, showIcon)
    end
end

-- 设置偏移
function GWHUDStrayDogBubbleObject:SetOffset(x, y, order)
    local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    XOffset = bubbleConfig.offsetX + x
    yOffset = bubbleConfig.offsetY + y
    self.order = order
    self:SetCanvasOrder()
end

-- 设置Canvas层级
function GWHUDStrayDogBubbleObject:SetCanvasOrder()
    self:AddLoadEvent(function()
        if util.IsObjNull(self.headCanvas) then
            return
        end
        self.headCanvas.sortingOrder = self.order or 0
    end)
end

-- 设置偏移缩放
function GWHUDStrayDogBubbleObject:SetOffsetScale(scale)
    self.offset_y = yOffset * scale
    self.offset_x = XOffset * scale
end

-- 设置独立跟随模式
function GWHUDStrayDogBubbleObject:SetupIndependentFollow()
    -- 从流浪狗下分离出来，移动到heroNode下
    if self.transform then
        self.transform.parent = GWG.GWHomeNode.heroNode()
    end

    -- 启动独立跟随更新
    self:StartIndependentFollowUpdate()
end

-- 开始独立跟随更新
function GWHUDStrayDogBubbleObject:StartIndependentFollowUpdate()
    if self.independentFollowTimer then
        util.RemoveDelayCall(self.independentFollowTimer)
    end

    local function updateFollow()
        if not util.IsObjNull(self.strayDogTransform) and not util.IsObjNull(self.transform) then
            -- 跟随流浪狗位置，但保持自己的旋转
            local dogPos = self.strayDogTransform.position
            local bubblePos = dogPos + Vector3(-1, 1.3, 0) -- 在流浪狗头上4.3个单位
            self.transform.position = bubblePos

            -- 面向摄像机并补偿倾斜角度
            local mainCamera = Camera.main
            if mainCamera then
                local cameraTransform = mainCamera.transform
                local cameraPos = cameraTransform.position

                -- 摄像机的倾斜角度
                local cameraTiltX = 40
                -- 计算水平方向（忽略Y轴差异）
                local direction = bubblePos - cameraPos
                direction.y = 0

                if direction.magnitude > 0.01 then
                    -- 基础水平旋转
                    local lookRotation = Quaternion.LookRotation(direction)
                    
                    local compensationRotation = Quaternion.AngleAxis(cameraTiltX, Vector3.right)
                    -- 应用补偿后的旋转
                    self.transform.rotation = lookRotation * compensationRotation
                end
            end

            -- 继续下一帧更新
            self.independentFollowTimer = util.DelayCallOnce(0.02, updateFollow) -- 50fps更新
        end
    end

    updateFollow()
end

-- 停止独立跟随更新
function GWHUDStrayDogBubbleObject:StopIndependentFollowUpdate()
    if self.independentFollowTimer then
        util.RemoveDelayCall(self.independentFollowTimer)
        self.independentFollowTimer = nil
    end
end

-- 获取随机表情图标（替代获取随机文本）
function GWHUDStrayDogBubbleObject:GetRandomEmotionIcon()
    -- 如果可用图标为空，重新初始化队列
    if #self.availableIcons == 0 then
        self.availableIcons = {}
        self.usedIcons = {}

        -- 复制所有图标到可用队列
        for _, iconName in ipairs(self.emotionIcons) do
            table.insert(self.availableIcons, iconName)
        end

        -- 打乱可用图标队列
        self:ShuffleIconQueue()
    end

    -- 从可用队列中取出第一个图标
    local selectedIcon = table.remove(self.availableIcons, 1)

    -- 移动到已使用队列
    table.insert(self.usedIcons, selectedIcon)

    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡选择图标: " .. selectedIcon .. ", 剩余可用图标: " .. #self.availableIcons)

    return selectedIcon
end

-- 打乱图标队列（Fisher-Yates洗牌算法）
function GWHUDStrayDogBubbleObject:ShuffleIconQueue()
    for i = #self.availableIcons, 2, -1 do
        local j = math.random(i)
        self.availableIcons[i], self.availableIcons[j] =
            self.availableIcons[j], self.availableIcons[i]
    end

    local iconList = table.concat(self.availableIcons, ", ")
    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡图标队列已打乱，顺序: " .. iconList)
end

-- 检查是否可以触发气泡
function GWHUDStrayDogBubbleObject:CanTriggerBubble(triggerType)
    if self.isShowing then
        return false -- 正在显示时不能触发新的
    end
    
    -- local currentTime = Time.time
    -- local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    -- local cooldown = bubbleConfig.cooldownTime

    -- -- 根据触发类型设置不同的CD
    -- if triggerType == "patrol" then
    --     cooldown = bubbleConfig.patrolCooldown
    -- elseif triggerType == "rest" then
    --     cooldown = bubbleConfig.restCooldown
    -- end
    
    -- return (currentTime - self.lastTriggerTime) >= cooldown

    return true
end

-- 显示气泡（使用图标替代文本）
function GWHUDStrayDogBubbleObject:ShowBubble(category, forceShow)
    -- 检查是否可以触发（强制显示时跳过检查）
    if not forceShow and not self:CanTriggerBubble(category) then
        return false
    end

    -- 获取随机表情图标
    local iconName = self:GetRandomEmotionIcon()
    if not iconName then
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡无可用图标")
        return false
    end

    self.transform.gameObject:SetActive(true)
    -- 设置图标并显示
    self:SetIcon(iconName)
    self:PlayAnim("ShowGuideBubble")

    -- 更新状态
    self.isShowing = true
    self.lastTriggerTime = Time.time

    -- 如果是强制显示（点击流浪狗），重置CD
    if forceShow then
        self.lastTriggerTime = Time.time
    end

    -- 设置自动隐藏计时器
    if self.hideTimer then
        util.RemoveDelayCall(self.hideTimer)
    end

    local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    self.hideTimer = util.DelayCallOnce(bubbleConfig.showDuration, function()
        self:HideBubble()
    end)

    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡显示成功，图标: " .. iconName)
    return true
end

-- 隐藏气泡
function GWHUDStrayDogBubbleObject:HideBubble()
    if not self.isShowing then
        return
    end
    
    self:PlayAnim("HideGuideBubble")
    self.isShowing = false
    
    -- 清理计时器
    if self.hideTimer then
        util.RemoveDelayCall(self.hideTimer)
        self.hideTimer = nil
    end
end

-- 交互时触发气泡（点击流浪狗，强制显示）
function GWHUDStrayDogBubbleObject:TriggerInteractBubble()
    return self:ShowBubble("interact", true) -- 强制显示
end

-- 更新配置（现在通过配置管理器）
function GWHUDStrayDogBubbleObject:UpdateConfig(config)
    -- 配置现在通过配置管理器统一管理
    -- 如果需要运行时更新配置，应该调用配置管理器的方法
    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡配置更新请求，建议通过配置管理器统一处理")

    -- 可以触发配置重新加载
    gw_home_config_mgr.ReloadConfig()
end

---清除数据
---@see override
function GWHUDStrayDogBubbleObject:ClearData()
    -- 清理计时器
    if self.hideTimer then
        util.RemoveDelayCall(self.hideTimer)
        self.hideTimer = nil
    end

    -- 停止独立跟随更新
    self:StopIndependentFollowUpdate()

    -- 重置状态
    self.isShowing = false
    self.lastTriggerTime = 0
    self.usedIcons = {}
    self.availableIcons = {}
    self.currentIconSet = nil

    -- 清理精灵资源
    self.spriteAsset = nil

    -- 重置偏移
    if not util.IsObjNull(self.parentTr) then
        UIUtil.SetAncoPosition(self.parentTr, -110, -32)
    end

    gw_home_comp_hud_base.ClearData(self)

    local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    XOffset = bubbleConfig.offsetX
    yOffset = bubbleConfig.offsetY
    self.offset_x = XOffset
    self.offset_y = yOffset
    self.order = 0
end

-- 暂停气泡系统
function GWHUDStrayDogBubbleObject:Pause()
    -- 停止独立跟随更新
    self:StopIndependentFollowUpdate()

    -- 停止隐藏计时器
    if self.hideTimer then
        util.RemoveDelayCall(self.hideTimer)
        self.hideTimer = nil
    end

    -- 隐藏气泡
    self:HideBubble()

    self.isPaused = true
end

-- 恢复气泡系统
function GWHUDStrayDogBubbleObject:Resume()
    self.isPaused = false

    -- 恢复独立跟随更新
    if self.strayDogTransform then
        self:StartIndependentFollowUpdate()
    end
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWHUDStrayDogBubbleObject
