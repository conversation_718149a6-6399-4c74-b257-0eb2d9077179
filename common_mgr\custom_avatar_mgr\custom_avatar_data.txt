local require = require
local table = table
local ipairs = ipairs
local log = require "log"
local game_scheme 	        = require "game_scheme"
local const = require "const"

module("custom_avatar_data")
logger = require("logger").new("custom_avatar_data", 0)
Warning = logger.Warning
IsLogLevel = logger.IsLogLevel


local allAvatarData = {}
local reviewedRejectData = nil
local myAvatarId = nil
local coolDownTime = 1741234332
local isUnlock = false
local isDirty = false
local isSystemClick = false

Custom_Config_Id = 1
Custom_Config_MAX_ID = 10

function SetMyCustomAvatarData(data)
    --Warning(3, "自定义头像", "登录初始化数据 ","customFaceTime:",data.customFaceTime,"customFaceFlag:",data.customFaceFlag)
    if data.customFaceTime then
        coolDownTime = data.customFaceTime
    end
    if data.customFaceFlag then
        isUnlock = data.customFaceFlag == 1
    end
end

function AddAvatar(imageId,remoteUrl,pos,status,used,isMyself)
    if not imageId then
        log.Error("avatar imageId is empty")
    end
    --Warning(3, "自定义头像", "添加头像",imageId,remoteUrl,pos,status,used)
    if status == const.Custom_Image_Enum.ReviewedReject or status == const.Custom_Image_Enum.MachineReviewedReject or status == const.Custom_Image_Enum.Deleted
            or status == const.Custom_Image_Enum.ExpiredReject then
        reviewedRejectData = {}
        reviewedRejectData.imageId = imageId or ""
        reviewedRejectData.remoteUrl = remoteUrl
        reviewedRejectData.pos = pos
        reviewedRejectData.status = status
        reviewedRejectData.used = used == 1 and true or false
        return
    end
    
    local faceData = {}
    faceData.imageId = imageId or ""
    faceData.remoteUrl = remoteUrl
    faceData.pos = pos
    faceData.status = status
    faceData.used = used == 1 and true or false
    if isMyself then
        myAvatarId = imageId
    end
    
    table.insert(allAvatarData,faceData)
end

----更新Avatar的审核状态和使用状态
function UpdateAvatarVerify(imageId, status, used)
    --先检查是不是审核拒绝的数据(审核失败会临时存储起来的)
    if reviewedRejectData ~= nil and reviewedRejectData.imageId == imageId and (status == 1 or status == 2) then
        AddAvatar(reviewedRejectData.imageId, reviewedRejectData.remoteUrl, reviewedRejectData.pos, status, used,true)
        reviewedRejectData = nil
        return
    end
    
    for k, v in ipairs(allAvatarData) do
        if v.imageId == imageId then
            if status ~= nil then
                v.status = status
            end
            if used ~= nil then
                v.used = used
            end
            return
        end
    end
end

----设置Avatar的url数据
function SetAvatarUrl(imageId, url)
    for k, v in ipairs(allAvatarData) do
        if v.imageId == imageId then
            v.localUrl = url
            return
        end
    end
end

----获取自己的avatar数据
function GetMyAvatar()
    return GetAvatar(myAvatarId)
end

----设置自己的avatar是否使用
function SetMyAvatarUsed(used)
    local data = GetAvatar(myAvatarId)
    if data then
        data.used = used
    end
    SetIsDirty(true)
end

----获取自己的avatar是否使用
function GetMyAvatarUsed()
    local data = GetAvatar(myAvatarId)
    if data then
        return data.used
    end
    return false
end

----根据id删除数据
function RemoveAvatar(imageId)
    for k,v in ipairs(allAvatarData) do
        if v.imageId == imageId then
            table.remove(allAvatarData,k)
            return
        end
    end
end

----暂存审核失败数据
function RemoveRejectedAvatar(imageId)
    for k,v in ipairs(allAvatarData) do
        if v.imageId == imageId then
            reviewedRejectData = v
            reviewedRejectData.status = const.Custom_Image_Enum.ReviewedReject
            table.remove(allAvatarData,k)
            return
        end
    end
end

--根据id获取数据
function GetAvatar(imageId)
    for k,v in ipairs(allAvatarData) do
        if v.imageId == imageId then
            return v
        end
    end
    return nil
end

----设置是否修改过
function SetIsDirty(offset)
    isDirty = offset
end

----获取是否修改过
function GetIsDirty()
    return isDirty
end

--设置冷却起始时间
function SetCoolDownStartTime(time)
    coolDownTime = time
    --Warning(3, "自定义头像", "设置冷却起始时间",coolDownTime)
end

function SetCustomAvatarActive()
    isUnlock = true
end

--是否在冷却中
function IsCoolingDown()
    --冷却时间加上配置表中的冷却时长，如果大于当前时间，则在冷却中
    local net_login_module = require "net_login_module"
    local cd = game_scheme:InitBattleProp_0(8263).szParam.data[0]
    if coolDownTime + cd > net_login_module.GetServerTime() then
        return true
    end
    return false
end

----刷新冷却事件
function RefreshCoolDownTime()
    local net_login_module = require "net_login_module"
    coolDownTime = net_login_module.GetServerTime()
end

--获取冷却结束的时间戳
function GetFinishTimeStamp()
    local cd = game_scheme:InitBattleProp_0(8263).szParam.data[0]
    local time = coolDownTime + cd
    return time
end

----上传成功后会帮玩家设置头像 这一次上报需要报0，其他情况下玩家选自定义头像报1
function GetAvatarIsSystemClick()
    return isSystemClick
end

function SetAvatarSystemClick(val)
    isSystemClick = val
end

function Clear()
    allAvatarData = {}
    myAvatarId = nil
    isDirty = false
    coolDownTime = 0
    reviewedRejectData = nil
end
