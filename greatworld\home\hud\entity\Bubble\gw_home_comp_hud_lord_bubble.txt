local GWG = GWG
local require = require
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local table = table
local GWConst = require "gw_const"
local util = require "util"
local lang = require "lang"
local print = print
local gw_home_comp_hud_base = require "gw_home_comp_hud_base"
local typeof = typeof
local gw_home_config_mgr = require "gw_home_config_mgr"
local Animator = CS.UnityEngine.Animator
local val = require("val")
local isPerf = val.IsTrue("sw_home_bubble_new", 0)

-- 添加Unity类型引用
local Camera = CS.UnityEngine.Camera
local RenderMode = CS.UnityEngine.RenderMode
local Vector3 = CS.UnityEngine.Vector3
local Quaternion = CS.UnityEngine.Quaternion
local Time   = CS.UnityEngine.Time
local ipairs = ipairs
local math = math
local GWG = GWG

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWHUDLordBubble : unit_base 领主气泡HUD对象
module("gw_home_comp_hud_lord_bubble")
local GWHUDLordBubbleObject = newclass("gw_home_comp_hud_lord_bubble", gw_home_comp_hud_base)

local XOffset = 89
local yOffset = 60


--- 构造器
function GWHUDLordBubbleObject:ctor()
    gw_home_comp_hud_base.ctor(self)
    
    -- 文本队列管理
    self.usedTexts = {}        -- 已使用的文本队列
    self.availableTexts = {}   -- 可用的文本队列
    self.currentCategory = nil -- 当前使用的文本类别
    
    -- 时间管理
    self.lastTriggerTime = 0   -- 上次触发时间
    self.hideTimer = nil       -- 隐藏计时器
    self.isShowing = false     -- 是否正在显示
end

---@public 设置数据
---@param bindParent table Transform 绑定的父节点
---@see override
function GWHUDLordBubbleObject:InitData(bindParent, data)
    gw_home_comp_hud_base.InitData(self, bindParent, data)

    -- 保存领主Transform引用，用于跟随
    if data and data.lordTransform then
        self.lordTransform = data.lordTransform
    end

    self:InstantiateModelAsync("art/greatworld/home/<USER>/hud/gwhomelordbubblehud.prefab", data.parent)
end

--- @public 基类方法实例化模型
---@param path string 模型路径
---@param parent table Transform 父节点
---@see override
function GWHUDLordBubbleObject:InstantiateModelAsync(path, parent)
    gw_home_comp_hud_base.InstantiateModelAsync(self, path, parent)
end

---实例化成功
---@param _obj table GameObject 模型对象
---@see override
function GWHUDLordBubbleObject:InstantiateSuccess(_obj)
    self.parentTr = UIUtil.GetComponent(_obj.transform, "RectTransform", "parent")
    self.headCanvas = UIUtil.GetComponent(_obj.transform, "Canvas")
    self.iconImg = UIUtil.GetComponent(_obj.transform, "Image", "parent/IconParent/Icon")
    self.iconParent = UIUtil.GetComponent(_obj.transform, "RectTransform", "parent/IconParent")
    self.animators = UIUtil.GetComponent(_obj.transform, typeof(Animator), "")
    self.infoText = UIUtil.GetComponent(_obj.transform, "Text", "parent/duihuakuan/content")
    self.infoText2 = UIUtil.GetComponent(_obj.transform, "Text", "parent/duihuakuan/content2")

    gw_home_comp_hud_base.InstantiateSuccess(self, _obj)

    -- 设置独立跟随模式
    self:SetupIndependentFollow()

    if isPerf then
        -- 设置聊天气泡在世界空间跟随
        self.transform.parent = self.parentTrans
    end

    -- 初始化时强制隐藏气泡
    self.transform.gameObject:SetActive(false)

    self.order = 2000
    self:SetCanvasOrder()

end

-- 播放动画
function GWHUDLordBubbleObject:PlayAnim(animName)
    if util.IsObjNull(self.animators) then
        return
    end
    self.animators:Play(animName)
end

-- 设置文本（领主气泡只使用infoText2，不显示头像）
function GWHUDLordBubbleObject:SetText(langId)
    self:AddLoadEvent(function()
        -- 隐藏头像，只显示文本
        self:RefreshIconLayout(false)
        self.infoText2.text = lang.Get(langId)
    end)
end

-- 刷新图标布局（领主气泡不显示图标）
function GWHUDLordBubbleObject:RefreshIconLayout(isIcon)
    if not util.IsObjNull(self.iconParent) then
        UIUtil.SetActive(self.iconParent.gameObject, false) -- 始终隐藏图标
    end
    if util.IsObjNull(self.infoText) or util.IsObjNull(self.infoText2) then
        return
    end
    UIUtil.SetActive(self.infoText, false)      -- 始终隐藏infoText
    UIUtil.SetActive(self.infoText2, true)      -- 始终显示infoText2
end

-- 设置偏移
function GWHUDLordBubbleObject:SetOffset(x, y, order)
    local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    XOffset = bubbleConfig.offsetX + x
    yOffset = bubbleConfig.offsetY + y
    self.order = order
    self:SetCanvasOrder()
end

-- 设置Canvas层级
function GWHUDLordBubbleObject:SetCanvasOrder()
    self:AddLoadEvent(function()
        if util.IsObjNull(self.headCanvas) then
            return
        end
        self.headCanvas.sortingOrder = self.order or 0
    end)
end

-- 设置偏移缩放
function GWHUDLordBubbleObject:SetOffsetScale(scale)
    self.offset_y = yOffset * scale
    self.offset_x = XOffset * scale
end

-- 设置独立跟随模式
function GWHUDLordBubbleObject:SetupIndependentFollow()
    --if util.IsObjNull(self.headCanvas) then
    --    return
    --end

    ---- 设置Canvas为World Space模式
    --self.headCanvas.renderMode = RenderMode.WorldSpace
    --
    ---- 设置Canvas的缩放，让气泡大小合适
    --self.headCanvas.transform.localScale = Vector3(0.01, 0.01, 0.01)

    -- 从领主下分离出来，移动到heroNode下
    if self.transform then
        self.transform.parent = GWG.GWHomeNode.heroNode()
    end

    -- 启动独立跟随更新
    self:StartIndependentFollowUpdate()
end

-- 开始独立跟随更新
function GWHUDLordBubbleObject:StartIndependentFollowUpdate()
    if self.independentFollowTimer then
        util.RemoveDelayCall(self.independentFollowTimer)
    end

    local function updateFollow()
        if not util.IsObjNull(self.lordTransform) and not util.IsObjNull(self.transform) then
            -- 跟随领主位置，但保持自己的旋转
            local lordPos = self.lordTransform.position
            local bubblePos = lordPos + Vector3(3, 4.3, 0) -- 在领主头上2个单位
            self.transform.position = bubblePos

            -- 面向摄像机并补偿倾斜角度
            local mainCamera = Camera.main
            if mainCamera then
                local cameraTransform = mainCamera.transform
                local cameraPos = cameraTransform.position

                -- 摄像机的倾斜角度
                local cameraTiltX = 40
                -- 计算水平方向（忽略Y轴差异）
                local direction = bubblePos - cameraPos
                direction.y = 0

                if direction.magnitude > 0.01 then
                    -- 基础水平旋转
                    local lookRotation = Quaternion.LookRotation(direction)
                    
                    local compensationRotation = Quaternion.AngleAxis(cameraTiltX, Vector3.right)
                    -- 应用补偿后的旋转
                    self.transform.rotation = lookRotation * compensationRotation
                end
            end

            -- 继续下一帧更新
            self.independentFollowTimer = util.DelayCallOnce(0.02, updateFollow) -- 50fps更新
        end
    end

    updateFollow()
end

-- 停止独立跟随更新
function GWHUDLordBubbleObject:StopIndependentFollowUpdate()
    if self.independentFollowTimer then
        util.RemoveDelayCall(self.independentFollowTimer)
        self.independentFollowTimer = nil
    end
end

-- 获取随机闲聊文本
function GWHUDLordBubbleObject:GetRandomChatText(category)
    local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    local langIds = bubbleConfig.chatLangIds[category]
    if not langIds or #langIds == 0 then
        return nil
    end
    
    -- 如果类别改变或可用文本为空，重新初始化队列
    if self.currentCategory ~= category or #self.availableTexts == 0 then
        self.currentCategory = category
        self.availableTexts = {}
        self.usedTexts = {}
        
        -- 复制所有文本到可用队列
        for _, langId in ipairs(langIds) do
            table.insert(self.availableTexts, langId)
        end
    end
    
    -- 从可用队列中随机选择一个
    local randomIndex = math.random(1, #self.availableTexts)
    local selectedLangId = self.availableTexts[randomIndex]
    
    -- 移动到已使用队列
    table.remove(self.availableTexts, randomIndex)
    table.insert(self.usedTexts, selectedLangId)
    
    return selectedLangId
end

-- 检查是否可以触发气泡
function GWHUDLordBubbleObject:CanTriggerBubble(triggerType)
    if self.isShowing then
        return false -- 正在显示时不能触发新的
    end
    
    local currentTime = Time.time
    local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    local cooldown = bubbleConfig.cooldownTime

    -- 根据触发类型设置不同的CD
    if triggerType == "patrol" then
        cooldown = bubbleConfig.patrolCooldown
    elseif triggerType == "rest" then
        cooldown = bubbleConfig.restCooldown
    end
    
    return (currentTime - self.lastTriggerTime) >= cooldown
end

-- 显示气泡
function GWHUDLordBubbleObject:ShowBubble(category, forceShow)
    -- 检查是否可以触发（强制显示时跳过检查）
    if not forceShow and not self:CanTriggerBubble(category) then
        return false
    end
    
    -- 获取随机文本
    local langId = self:GetRandomChatText(category)
    if not langId then
        return false
    end
    self.transform.gameObject:SetActive(true)
    -- 设置文本并显示
    self:SetText(langId)
    self:PlayAnim("ShowGuideBubble")
    
    -- 更新状态
    self.isShowing = true
    self.lastTriggerTime = Time.time
    
    -- 如果是强制显示（点击领主），重置CD
    if forceShow then
        self.lastTriggerTime = Time.time
    end
    
    -- 设置自动隐藏计时器
    if self.hideTimer then
        util.RemoveDelayCall(self.hideTimer)
    end
    
    local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    self.hideTimer = util.DelayCallOnce(bubbleConfig.showDuration, function()
        self:HideBubble()
    end)
    
    return true
end

-- 隐藏气泡
function GWHUDLordBubbleObject:HideBubble()
    if not self.isShowing then
        return
    end
    
    self:PlayAnim("HideGuideBubble")
    self.isShowing = false
    
    -- 清理计时器
    if self.hideTimer then
        util.RemoveDelayCall(self.hideTimer)
        self.hideTimer = nil
    end
end

-- 巡逻时触发气泡
function GWHUDLordBubbleObject:TriggerPatrolBubble()
    return self:ShowBubble("patrol", false)
end

-- 休息时触发气泡
function GWHUDLordBubbleObject:TriggerRestBubble()
    return self:ShowBubble("rest", false)
end

-- 交互时触发气泡（点击领主，强制显示）
function GWHUDLordBubbleObject:TriggerInteractBubble()
    return self:ShowBubble("interact", true) -- 强制显示
end

-- 寻路时触发气泡
function GWHUDLordBubbleObject:TriggerMoveBubble()
    return self:ShowBubble("move", false)
end

-- 更新配置（现在通过配置管理器）
function GWHUDLordBubbleObject:UpdateConfig(config)
    -- 配置现在通过配置管理器统一管理
    -- 如果需要运行时更新配置，应该调用配置管理器的方法
    GWG.GWAdmin.SwitchUtility.HomeLog("气泡配置更新请求，建议通过配置管理器统一处理")

    -- 可以触发配置重新加载
    gw_home_config_mgr.ReloadConfig()
end

---清除数据
---@see override
function GWHUDLordBubbleObject:ClearData()
    -- 清理计时器
    if self.hideTimer then
        util.RemoveDelayCall(self.hideTimer)
        self.hideTimer = nil
    end

    -- 停止独立跟随更新
    self:StopIndependentFollowUpdate()

    -- 重置状态
    self.isShowing = false
    self.lastTriggerTime = 0
    self.usedTexts = {}
    self.availableTexts = {}
    self.currentCategory = nil

    -- 重置偏移
    if not util.IsObjNull(self.parentTr) then
        UIUtil.SetAncoPosition(self.parentTr, -110, -32)
    end

    gw_home_comp_hud_base.ClearData(self)

    local bubbleConfig = gw_home_config_mgr.GetBubbleConfig()
    XOffset = bubbleConfig.offsetX
    yOffset = bubbleConfig.offsetY
    self.offset_x = XOffset
    self.offset_y = yOffset
    self.order = 0
end

-- 暂停气泡系统
function GWHUDLordBubbleObject:Pause()
    -- 停止独立跟随更新
    self:StopIndependentFollowUpdate()

    -- 停止隐藏计时器
    if self.hideTimer then
        util.RemoveDelayCall(self.hideTimer)
        self.hideTimer = nil
    end

    -- 隐藏气泡
    self:HideBubble()

    self.isPaused = true
end

-- 恢复气泡系统
function GWHUDLordBubbleObject:Resume()
    self.isPaused = false

    -- 恢复独立跟随更新
    if self.lordTransform then
        self:StartIndependentFollowUpdate()
    end
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWHUDLordBubbleObject
