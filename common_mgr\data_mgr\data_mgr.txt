﻿---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by fangy21120206.
--- DateTime: 2024/5/21 17:29
--- DEPRECATED 只做数据获取与清理不做数据设置
local setmetatable = setmetatable
local log = require "log"
local data_base = require "data_base"
module("data_mgr")
local M = {}
M.d_m_list = {}

---@see 创建数据对象
function M:CreateData(name)
    if self.d_m_list[name] then
        return self.d_m_list[name]
    end
    local new_data = setmetatable({},{ __index = data_base })
    new_data:ctor(name)
    self.d_m_list[name] = new_data
    return new_data
end

---@see 获取数据对象
---@param name 类名
function M:GetData(name)
    local d_m_list = self.d_m_list[name]
    if not d_m_list then
        log.Error("data_mgr:GetData error not create",name)
        return
    end
    return d_m_list
end


---@see 清理所有数据
function M:ClearAll()
    for k,v in pairs(self.d_m_list) do
        v:ClearAll()
    end
    self.d_m_list = {}
end

return M