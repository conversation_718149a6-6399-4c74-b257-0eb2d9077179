---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by hans<PERSON><PERSON>.
--- DateTime: 2024/8/23 9:50
--- Desc: 修复气泡类

local require = require
local tonumber = tonumber
local string = string
local pairs = pairs
local table = table
local ipairs = ipairs
local newclass = newclass
local gw_home_novice_const = require "gw_home_novice_const"
local lang = require "lang"
local flow_text = require "flow_text"
local util = require "util"
local GWConst = require "gw_const"
local gw_home_comp_bubble_util = require "gw_home_comp_bubble_util"
local event = require "event"
local player_mgr = require "player_mgr"
local net_city_module = require "net_city_module"
local game_scheme = require "game_scheme"
local gw_home_comp_build_bubble_base = require "gw_home_comp_build_bubble_base"
local UIUtil = CS.Common_Util.UIUtil
local gw_home_card_sprite_asset_mgr = require "gw_home_card_sprite_asset_mgr"
local GWG = GWG
local windowMgr = require "ui_window_mgr"
module("gw_home_comp_bubble_repair")
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWHomeCompBubbleRepair : GWDisposableObject
local GWHomeCompBubbleRepair = newclass("gw_home_comp_bubble_repair", gw_home_comp_build_bubble_base)
--修复条件（条件类型1 建筑id#建筑等级  条件类型2 关卡id  条件类型3 小游戏关卡id  条件类型4 礼包id）
local FixConditionTypes = {
    BUILDING_ID_AND_LEVEL = 1, -- 条件类型1: 建筑id#建筑等级
    STAGE_ID = 2, -- 条件类型2: 关卡id
    MINI_GAME_STAGE_ID = 3, -- 条件类型3: 小游戏关卡id
    GIFT_PACKAGE_ID = 4       -- 条件类型4: 礼包id
}

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

function GWHomeCompBubbleRepair:InitData(buildData, bindParent, parent, bubbleType)
    gw_home_comp_build_bubble_base.InitData(self, buildData, bindParent, bubbleType)
    if util.IsObjNull(self.bubbleSpriteAsset) then
        self.bubbleSpriteAsset = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("gwbubble")
    end
    if util.IsObjNull(self.goodsSpriteAsset) then
        self.goodsSpriteAsset = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("GoodsAssetPath")
    end
    self:InstantiateModelAsync("ui/prefabs/gw/buildsystem/bubbleui/bubblerepairui.prefab", parent);
end

function GWHomeCompBubbleRepair:InstantiateSuccess(_obj)
    self.bgImage = UIUtil.GetComponent(_obj.transform, "Image", "Image")
    self.numberText = UIUtil.GetComponent(_obj.transform, "Text", "Text");
    gw_home_comp_build_bubble_base.InstantiateSuccess(self, _obj)
    self.bgRectTr = UIUtil.GetComponent(_obj.transform, "RectTransform", "Image")
    self:RefreshBuildLevelTypeData(self.buildData)
end

function GWHomeCompBubbleRepair:RefreshSize(bubbleType)
    local typeSize = gw_home_comp_bubble_util.FunctionCFGConst[bubbleType]
    if not typeSize then
        typeSize = gw_home_comp_bubble_util.FunctionCFGConst[GWConst.EHomeBubbleEntityType.None]
    end
    if util.IsObjNull(self.bgRectTr) then
        return
    end
    self.bgRectTr.sizeDelta = typeSize.size
    self.bgRectTr.anchoredPosition = typeSize.pos
end

---@public 刷新建筑等级类型数据 展示气泡内容
function GWHomeCompBubbleRepair:RefreshBuildLevelTypeData(buildData)
    if buildData == nil then
        return
    end
    --初始化数据
    if not buildData then
        return
    end
    if not self.preConsumeData then
        self.preConsumeData = {}
        local cfg = game_scheme:Building_0(buildData.nBuildingID, buildData.nLevel + 1)
        if not cfg then
            GWG.GWAdmin.SwitchUtility.Error("BuildLevelTypeData not cfg", buildData.nBuildingID, buildData.nLevel)
            return
        end
        if cfg.food and cfg.iron and cfg.money then
            if cfg.food and cfg.food > 0 then
                local itemData = {}
                itemData.id = 35;
                itemData.needNumber = cfg.food
                itemData.number = player_mgr.GetPlayerFood()
                itemData.missNumber = itemData.needNumber - itemData.number
                table.insert(self.preConsumeData, itemData)
            end
            if cfg.iron and cfg.iron > 0 then
                local itemData = {}
                itemData.id = 36;
                itemData.needNumber = cfg.iron
                itemData.number = player_mgr.GetPlayerIron()
                itemData.missNumber = itemData.needNumber - itemData.number
                table.insert(self.preConsumeData, itemData)
            end
            if cfg.money and cfg.money > 0 then
                local itemData = {}
                itemData.id = 1;
                itemData.needNumber = cfg.money
                itemData.number = player_mgr.GetPlayerCoin()
                itemData.missNumber = itemData.needNumber - itemData.number
                table.insert(self.preConsumeData, itemData)
            end
            if cfg.cost then
                local Arr = string.split(cfg.cost, ";")
                for _, v in pairs(Arr) do
                    local arr = string.split(v, "#")
                    local itemData = {}
                    itemData.id = tonumber(arr[1])
                    itemData.needNumber = tonumber(arr[2])
                    itemData.number = player_mgr.GetPlayerOwnNum(itemData.id)
                    itemData.missNumber = itemData.needNumber - itemData.number
                    table.insert(self.preConsumeData, itemData)
                end
            end
            -- 使用table.sort和自定义比较函数进行排序
            table.sort(self.preConsumeData, function(a, b)
                -- 注意这里的比较是b.missNumber - a.missNumber，以实现从大到小排序
                return b.missNumber > a.missNumber
            end)
        end
    end
    --刷新显示
    self.isRepair = true

    for i, v in ipairs(self.preConsumeData) do
        v.number = player_mgr.GetPlayerOwnNum(v.id)
        v.missNumber = v.needNumber - v.number
    end
    for i, v in ipairs(self.preConsumeData) do
        if v.missNumber > 0 then
            self.isRepair = false
            self.numberText.text = v.missNumber
            local itemCfg = game_scheme:Item_0(v.id)
            if itemCfg then
                self.goodsSpriteAsset:GetSprite(itemCfg.icon, function(sprite)
                    self:RefreshSize(GWConst.EHomeBubbleEntityType.Resource)
                    self.bgImage.sprite = sprite
                end)
                self:SetCheckState(false)
            end
            break
        end
    end
    if self.isRepair then
        self.numberText.text = string.empty()
        --显示修复气泡 图标
        local repairCfg = gw_home_comp_bubble_util.FunctionCFGConst[GWConst.EHomeBubbleEntityType.REPAIR]
        self.bubbleSpriteAsset:GetSprite(repairCfg.path, function(sprite)
            self:RefreshSize(GWConst.EHomeBubbleEntityType.REPAIR)
            self.bgImage.sprite = sprite
        end)
        self:SetCheckState(true)
    end
end

function GWHomeCompBubbleRepair:RegisterListener()
    self.refreshRepair = function(key, value)
        self:RefreshBuildLevelTypeData(self.buildData)
    end
    event.Register(event.UPDATE_PLAYER_PROP_INFO, self.refreshRepair)
end

function GWHomeCompBubbleRepair:UnregisterListener()
    event.Unregister(event.UPDATE_PLAYER_PROP_INFO, self.refreshRepair)
end

---@public 检测气泡
---@see override
function GWHomeCompBubbleRepair:CheckFuncState()
    return false
end

---@public 点击事件
---@see override
function GWHomeCompBubbleRepair:OnClick()
    ----判断是否有队列 修复不判断有没有队列
    --if not GWG.GWHomeMgr.buildingQueueData.HasFreeQueue() then
    --    --没有空闲队列 
    --    windowMgr:ShowModule("ui_bs_new_que")
    --    return
    --end
    local show = GWG.GWAdmin.GWHomeCfgUtil.GetNoviceIsRepair(self.buildData.nBuildingID)
    if not show then
        flow_text.Add(lang.Get(607334))
        return
    end
    if self.isRepair then
        --发送修复建筑事件
        if self.buildData.nLevel <= 0 then
            local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
            local force_guide_system = require "force_guide_system"
            local force_guide_event = require "force_guide_event"
            if gw_home_novice_chapter_data.GetNoviceAndNoCheckPassLevel() then
                if self.buildData.uMapUnitID == gw_home_novice_const.Novice_Home_Wall_CityId then
                    force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventBubbleClick3)
                end
            end
            if self.buildData.uMapUnitID == gw_home_novice_const.Novice_Home_Main_MapCityId then
                force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventBubbleClick7)
            end
        end
        net_city_module.MSG_CITY_REPAIR_A_BUILDING_REQ(self.buildData.uSid)
    else
        --弹出修复界面
        windowMgr:ShowModule("ui_bs_upgrade", nil, nil, self.buildData)
    end
end

function GWHomeCompBubbleRepair:ClearData()
    self.goodsSpriteAsset = nil
    self.bubbleSpriteAsset = nil
    self.preConsumeData = nil
    self.refreshRepair = nil
    self.playerProp = nil
    self.isRepair = false
    self.numberText = nil
    gw_home_comp_build_bubble_base.ClearData(self)
end

return GWHomeCompBubbleRepair