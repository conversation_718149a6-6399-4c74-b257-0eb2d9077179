﻿---
--- Generated by Emmy<PERSON>ua(https://github.com/EmmyLua)
--- Created by chenyong2024.
--- DateTime: 2025/3/3 下午3:49
---
---
local require = require
local table = table
local print = print
local string = string
local os = os
local math     = math
local custom_avatar_data = require "custom_avatar_data"
local lang                = require "lang"
local BaseLoader = CS.War.Base.BaseLoader
local Directory = CS.System.IO.Directory
local IO = CS.System.IO
local Application = CS.UnityEngine.Application
local File = CS.System.IO.File
local event = require "event"
local event_personalInfo = require "event_personalInfo"
local net_personalInfo = require "net_personalInfo"
local lang_res_key          = require "lang_res_key"
local game_scheme 	        = require "game_scheme"
local util              = require "util"
local http_inst = require "http_inst"
local pairs     = pairs
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility

module("custom_avatar_mgr")

-- 开启log日志
logger = require("logger").new("custom_avatar_mgr", 0)
IsLogLevel = logger.IsLogLevel
Warning = logger.Warning
Warning0 = logger.Warning0

local LOAD_TIMEOUT = 2
local REDOWNLOAD_COUNT = 3 --重新下载次数
local MAX_IMAGE_MEMORY_NUM = 200 --最大的头像内存缓存数量
local DEL_IMAGE_MEMORY_NUM = 20 --每次清理内存的数量
local spriteAssetList = {}
local NoCheckPreconditions = false

function Init()
    --用户数据重置
    event.Register(event.USER_DATA_RESET, Dispose)

    --请求上传图片
    event.Register(event_personalInfo.UPLOAD_CUSTOM_AVATAR_REQUEST, MSG_CUSTOM_FACE_UPLOAD_RSP)
    --图片信息给服务器后返回
    event.Register(event_personalInfo.UPLOAD_CUSTOM_AVATAR_RESULT_REPORT, MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP)
    ----更新头像审核结果
    event.Register(event_personalInfo.CUSTOM_AVATAR_VERIFY, MSG_CUSTOM_FACE_VERIFY_NTF)
    --更新功能是否激活
    event.Register(event_personalInfo.CUSTOM_AVATAR_ACTIVE, MSG_CUSTOM_FACE_ACTIVATE_NTF)

    --更新头像使用
    event.Register(event_personalInfo.CUSTOM_AVATAR_USE, MSG_CUSTOM_FACE_USE_RSP)
    
    
end

--启动自定义头像
function CustomizeAvatar()
    event.EventReport("CustomizeHead_Click", { })
    
    --检查前置条件
    if not CheckPreconditions() then
        print("无法上传头像" )
        Warning(3, "自定义头像", "无法上传头像")
        return
    end
    --正常流程 先请求服务器 检查是否可以上传
    --目前只有一个位置可以上传头像 postion = 1
    OpenTipsMessageBox(650081,650082,function()
        net_personalInfo.MSG_CUSTOM_FACE_UPLOAD_REQ(1)
    end)
end

--启动自定义头像（服务器请求成功）
function MSG_CUSTOM_FACE_UPLOAD_RSP(_,msg)
    if Application.isEditor then
        -- (模拟SDK调用成功了)
        Warning(3, "自定义头像", "拉起上传SDK 模拟成功")
        local name = "PHOTOS/2162/1453/14532619820250310204915539.jpg"
        local fakeMsg = {id = "14532619820250310204915539", objectName= name, url="https://q1-operation-bulletin.oss-cn-beijing.aliyuncs.com/"..name}
        event.EventReport("CustomizeHead_Upload_Success", { })
        net_personalInfo.MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ(fakeMsg.id,fakeMsg.url,1)
    else
        local imageId = GenerateImageID()
        Warning(3, "自定义头像", "拉起上传SDK",imageId)
        local q1sdk = require "q1sdk"
        q1sdk.UploadImage(imageId, function(bSuccess, code, msg)
            Warning(3, "自定义头像", "上传图片结果",bSuccess, code, msg)
            if bSuccess then
                event.EventReport("CustomizeHead_Upload_Success", { })
                local json = require "dkjson"
                local jsonData = json.decode(msg)
                local id = jsonData["id"]
                local url = jsonData["url"]
                Warning(3, "自定义头像", "请求MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ",id,url)
                net_personalInfo.MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_REQ(id,url,1)
            else
                event.EventReport("CustomizeHead_Upload_Fail", { })
                local flow_text = require "flow_text"
                flow_text.Add(lang.Get(650084))
                Warning(3, "自定义头像", "上传图片失败",code, msg)
            end
        end)
    end
end

--上传图片信息给服务器的返回
function MSG_CUSTOM_FACE_UPLOAD_RESULT_REPORT_RSP(_,msg)
    if msg and msg.data then
        local imageId = msg.data.imageId or ""
        local url = msg.data.curl or ""
        local pos = msg.data.pos or 1
        local status = msg.data.status
        local used = msg.data.used
        --保存数据到本地data
        custom_avatar_data.AddAvatar(imageId,url,pos,status,used,true)
        custom_avatar_data.RefreshCoolDownTime()
        Warning(3, "自定义头像", "上传图片信息给服务器的返回",msg.data.imageId , msg.data.curl , msg.data.pos , msg.data.status , msg.data.used)
        --下载图片到本地
        DownloadImage(url,imageId)
    end
    --刷新cd时间
    if msg.customFaceTime then
        
    end
end

function MSG_CUSTOM_FACE_VERIFY_NTF()
    --检查是否有通过的自定义头像
    local const = require "const"
    local data = custom_avatar_data.GetMyAvatar()
    if data~=nil then
        if data.status == const.Custom_Image_Enum.Reviewed or data.status == const.Custom_Image_Enum.MachineReviewedPass then
            CheckAvatarLocalUrl(data.remoteUrl,data.imageId)
            net_personalInfo.MSG_CUSTOM_FACE_USE_REQ(data.imageId,1)
            --刷新主界面的头像
            --event.Trigger(event_personalInfo.UPDATE_PERSONALISED_INFO)
        else
            --审核不通过，提示下，然后删除数据，刷新显示
            local flow_text = require "flow_text"
            flow_text.Add(lang.Get(650091))
            DelImage(data.imageId)
            custom_avatar_data.RemoveRejectedAvatar(data.imageId)
            --net_personalInfo.MSG_CUSTOM_FACE_REMOVE_REQ(data.imageId,data.pos)
        end
    end
end

--自定义头像是否激活
function MSG_CUSTOM_FACE_ACTIVATE_NTF(_,msg)
    if msg.customFaceFlag then
        custom_avatar_data.SetCustomAvatarActive()
        Warning(3, "自定义头像", "激活")
    else
        Warning(3, "自定义头像", "不激活")
    end
end

--自定义头像是否使用
function MSG_CUSTOM_FACE_USE_RSP(_,msg)
    if msg.data.imageId then
        local data = custom_avatar_data.GetAvatar(msg.data.imageId)
        data.used = true
    end
end

--主动请求平台 获取图片审核状态
function getImageReviewStatus(imageId)
    local q1sdk = require "q1sdk"
    q1sdk.GetImageReviewStatus(imageId, function(bSuccess, code, msg)
        Warning(3, "自定义头像","获取图片审核状态",bSuccess, code, msg)
    end)
end

---检查前置条件
function CheckPreconditions()
    if not isModuleOpen() and not NoCheckPreconditions then
        Warning(3, "自定义头像", "首充功能未开启，无法上传头像！")
        OpenTipsMessageBox(650078,lang_res_key.KEY_OK)
        return false
    end
    if not isFirstRechargeFinish() and not NoCheckPreconditions then
        Warning(3, "自定义头像", "玩家未完成首充，无法上传头像！")
        OpenTipsMessageBox(650079,650080,function(d,r)
            local message_box = require "message_box"
            if r == message_box.RESULT_YES then
                local gw_firstrecharge_mgr = require "gw_firstrecharge_mgr"
                gw_firstrecharge_mgr.FirstRechargeOpenView()
            end
        end)
        return false
    end
    local myData = custom_avatar_data.GetMyAvatar()
    if myData and myData.imageId then
        local const = require "const"
        if myData.status ~= const.Custom_Image_Enum.Reviewed or myData.status == const.Custom_Image_Enum.MachineReviewedPass then
            local flow_text = require "flow_text"
            flow_text.Add(lang.Get(650088))
            return false
        end
    end
    if custom_avatar_data.IsCoolingDown() then
        Warning(3, "自定义头像", "冷却时间未结束，无法上传头像！")
        OpenCoolingMessageBox()
        return false
    end
 
   
    return true
end

----检查模块是否打开
function isModuleOpen()
    local gw_firstrecharge_mgr = require "gw_firstrecharge_mgr"
    return gw_firstrecharge_mgr.CheckIsActivityOpen()
end

----检查首充是否完成
function isFirstRechargeFinish()
    local gw_firstrecharge_mgr = require "gw_firstrecharge_mgr"
    return gw_firstrecharge_mgr.CheckIsFirstRechargeFinish()
end

----打开冷却时间的对话框
function OpenCoolingMessageBox()
    local message_box = require "message_box"
    message_box.SetUISkin("ui/prefabs/uimessageboxcountdown.prefab")
    local contentTxt = lang.Get(650085)
    local countDownTxt = lang.Get(602150)
    message_box.OpenCountdown(countDownTxt,custom_avatar_data.GetFinishTimeStamp(),contentTxt, message_box.STYLE_YES, function(callbackData, nRet)
    end, 0, lang.KEY_OK,lang.KEY_CANCEL,lang.KEY_SYSTEM_TIPS,nil,nil,nil,nil,nil)
end

---打开提示的对话框
function OpenTipsMessageBox(contentLangId,btnLangId,callback)
    local message_box = require "message_box"
    local contentTxt = lang.Get(contentLangId)
    local btnTxt = lang.Get(btnLangId)
    message_box.SetUISkin("ui/prefabs/uimessageboxblue.prefab")
    message_box.OpenCustomAvatarTips(contentTxt, message_box.STYLE_YES, callback,btnTxt)
end

local saveRootPath = nil
function GetPatchSaveRootPath()
    if saveRootPath then
        return saveRootPath
    end

    local persistentRelativeUrl = BaseLoader.GetPersistentRelativeUrl()
    persistentRelativeUrl = string.gsub(persistentRelativeUrl, "file://", "")
    saveRootPath = persistentRelativeUrl .. "/AvatarCaching/"
    if not Directory.Exists(saveRootPath) then
        Directory.CreateDirectory(saveRootPath)
    end
    return saveRootPath
end

function GenerateImageID()
    local player_mgr = require "player_mgr"
    local roleID = player_mgr.GetPlayerRoleID()
    local setting_server_data = require "setting_server_data"
    local serverID = setting_server_data.GetLoginWorldID() or ""
    -- 获取时间戳（毫秒级精度）
    local timestamp = os.time()
    local milliseconds = math.floor(os.clock() * 1000) % 1000
    local time_part = os.date("%Y%m%d%H%M%S", timestamp) .. string.format("%03d", milliseconds)

    -- 拼接唯一ID
    local unique_id = string.format("%s%s%s", serverID,roleID,time_part)
    return unique_id
end

---下载图片
function DownloadImage(url,id,callback)
    local http_inst = require "http_inst"
    
    local rootPath = GetPatchSaveRootPath()
    --local val = util.EncodeBase64(id)
    local saveFilePath = rootPath .. id..".jpg"
    
    local requestAsync,localExist
    requestAsync,localExist = http_inst.Request_DownloadFile(url, LOAD_TIMEOUT, function(subPatchSaveFilePath, errMsg, downloadedBytes)
        local noErr = not errMsg or errMsg == ''
        if not noErr then
            Warning(3, "自定义头像", "下载图片失败",errMsg)
        else
            subPatchSaveFilePath = "file://"..subPatchSaveFilePath
            --下载成功后 保存本地图片地址数据
            custom_avatar_data.SetAvatarUrl(id,subPatchSaveFilePath)
            if callback then 
                callback(subPatchSaveFilePath)
            end
            --刷新显示
            event.Trigger(event_personalInfo.UPDATE_CUSTOM_AVATAR_STATUS)
            Warning(5, "自定义头像", "下载图片成功 ",subPatchSaveFilePath)
            --下载完后检查缓存数量是否超过限制
            CheckStorage()
        end
    end, saveFilePath, false,nil,REDOWNLOAD_COUNT)
end

function CheckAvatarLocalUrl(url,imageId,callback)
    if not imageId or imageId == "" then
        Warning(3, "自定义头像", "CheckAvatarLocalUrl imageId is nil or empty")
        if callback then 
            callback(nil)
        end
        return
    end
    
    local rootPath = GetPatchSaveRootPath()
    local filepath = rootPath .. imageId..".jpg"
    
    if File.Exists(filepath) then
        filepath = "file://"..filepath
        custom_avatar_data.SetAvatarUrl(imageId,filepath)
        if callback then 
            callback(filepath)
        end
        --Warning(3, "自定义头像", "CheckAvatarLocalUrl",filepath)
    else
        DownloadImage(url,imageId,callback)
    end
end


---传入icon对象和local url，设置头像图片。超过设定值会清理内存里的图。
function SetAvatarIcon(icon,localUrl,callback)
    if not string.IsNullOrEmpty(localUrl) and not util.IsObjNull(icon) then
        http_inst.DownLoadImageNew(localUrl,function (sprite)
            if not util.IsObjNull(icon) then
                --记录这个localUrl使用的时间，键值对形式存在spriteAssetList中
                spriteAssetList[localUrl] = os.time()

                if not icon.Index and not icon.Version then --区分是否entity
                    icon.sprite = sprite
                    if not icon.sprite then
                        local log = require "log"
                        log.Error(icon.name,"icon sprite is nil",iconName)
                    end
                else
                    EntityHybridUtility.SetSpriteRectParamSafe(icon, sprite)
                    --EntityHybridUtility.SetSpritePivotScale(icon, sprite)
                end
                
                --如果spriteAssetList数量大于200，清理最早的20个
                local count = util.TableCount(spriteAssetList)
                if  count> MAX_IMAGE_MEMORY_NUM then
                    local oldestKeys = {}
                    for k, _ in pairs(spriteAssetList) do
                        table.insert(oldestKeys, k)
                    end
                    table.sort(oldestKeys, function(a, b) return spriteAssetList[a] < spriteAssetList[b] end)
                    for i = 1, math.min(DEL_IMAGE_MEMORY_NUM, #oldestKeys) do
                        http_inst.ClearImageCache(oldestKeys[i])
                        spriteAssetList[oldestKeys[i]] = nil
                        Warning(3, "自定义头像", "清理图片",oldestKeys[i])
                    end
                end
                if callback then
                    callback(sprite)
                end
            end
        end,2,5)
    end
end


---删除头像图片
function DelImage(id)
    local rootPath = GetPatchSaveRootPath()
    local saveFilePath = rootPath .. id..".jpg"
    if File.Exists(saveFilePath) then
        File.Delete(saveFilePath)
    end
end


---删除自定义头像
function DelCustomAvatar()
    Warning(3, "自定义头像", "删除自定义头像")
    local message_box = require "message_box"
    message_box.SetUISkin("ui/prefabs/uimessageboxblue.prefab")
    message_box.OpenCustomAvatarTips(lang.Get(650089), message_box.STYLE_YESNO, function(d,r)
        if r == message_box.RESULT_YES then
            local myData = custom_avatar_data.GetMyAvatar()
            if myData~=nil then
                event.EventReport("CustomizeHead_Delete", { })
                net_personalInfo.MSG_CUSTOM_FACE_REMOVE_REQ(myData.imageId,myData.pos)
                local flow_text = require "flow_text"
                flow_text.Add(lang.Get(650090))
                DelImage(myData.imageId)
            end
        end
    end, lang.Get(602001))
end

local MaxImageNum = nil
local DelImageNum = nil
----针对缓存限制的处理，一个是对缓存图片的大小进行统计，一个是对缓存图片的数量进行统计
----图片大小达到50M这个需要for循环计算其实没啥必要，直接对图片的数量统计就好，因为SDK会保证图片的大小小于50KB，而只要图片数量少于1024即可。而且图片压缩不会准确到50的，经常是20kb-30kb之间.
function CheckStorage()
    local files = GetAllFiles()

    local storageMax = game_scheme:InitBattleProp_0(8275).szParam.data[0] or 2200
    local delImage = game_scheme:InitBattleProp_0(8276).szParam.data[0] or 200
    if storageMax and not MaxImageNum then
        MaxImageNum = storageMax
    end
    if delImage and not DelImageNum then
        DelImageNum = delImage
    end
    if #files > MaxImageNum then
        CleanupFiles(files)
    end
end

----获取全部图片
function GetAllFiles()
    local fileList = {}
    local rootPath = GetPatchSaveRootPath()
    local csharpFiles = Directory.GetFiles(rootPath)

    for i = 0, csharpFiles.Length-1 do
        local filePath = csharpFiles[i]
        local fileInfo = IO.FileInfo(filePath)
        table.insert(fileList, {
            path = filePath,
            size = fileInfo.Length,
            created = fileInfo.CreationTime.Ticks
        })
    end

    -- 按创建时间升序排列（越早越靠前）
    table.sort(fileList, function(a,b)
        return a.created < b.created
    end)

    return fileList
end

---- 文件清理逻辑
function CleanupFiles(sortedFiles)
    local deleteCount = math.min(DelImageNum, #sortedFiles)

    local myAvatar = custom_avatar_data.GetMyAvatar()
    local myFilename = ""
    if myAvatar and myAvatar.localUrl then
        myFilename = ExtractFilename(myAvatar.localUrl)
    end
    for i = 1, deleteCount do
        local target = sortedFiles[i]
        if target and target.path then
            local filename = ExtractFilename(target.path)
            --自己头像的文件就不清理了
            if filename ~= myFilename then
                File.Delete(target.path)
                Warning(3, "自定义头像", "删除头像",target.path)
            end
        end
    end
end

----截取尾部的文件名 用于对比
function ExtractFilename(fullpath)
    if not fullpath or fullpath == "" then
        return ""
    end
    local filename = fullpath:match("[^/\\]+$")
    return filename and filename:lower() or ""
end

function Dispose()
    Warning(3, "自定义头像", "数据清除")
    custom_avatar_data.Clear()
    spriteAssetList = {}
    http_inst.ClearAllImageCache()
end

function SetNoCheckCheat()
    NoCheckPreconditions = true
end
