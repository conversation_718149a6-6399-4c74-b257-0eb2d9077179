---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by fgy.
--- DateTime: 2024/8/30 20:15
--- Desc: 个人标记

local require = require
local newclass = newclass
local tostring = tostring
local pairs = pairs
local UIUtil = UIUtil
local string = string
local xpcall = xpcall
local Quaternion = CS.UnityEngine.Quaternion
local Vector3 = CS.UnityEngine.Vector3
local SimpleTextOutline = CS.TextMeshUtil.SimpleTMOutline
local TextMesh  = CS.UnityEngine.TextMesh
local SpriteFitSize = CS.TextMeshUtil.SpriteFitSize
local SpriteRenderer = CS.UnityEngine.SpriteRenderer
local MeshRenderer = CS.UnityEngine.MeshRenderer

local gw_sand_simple_model_util = require "gw_sand_simple_model_util"
local util = require "util"
local sandbox_pb = require "sandbox_pb"
local common_pb = require "common_pb"
local sand_hud_base = require "sand_hud_base"
local sand_face_item = require "sand_face_item"
local gw_const = require "gw_const"
local ESMapResMarchKey = gw_const.ESMapResMarchKey
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local typeof = typeof
local SandEntityBase = require "sand_entity_base"
local entity_manager = require "entity_manager"

module("gw_comp_player_info_hud")

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWCompPlayerInfoHud : GWDisposableObject
local GWCompPlayerInfoHud = newclass("gw_comp_player_info_hud", sand_hud_base)

function GWCompPlayerInfoHud:InitHudComp(hudData, data)
    if data.enableEcs then
        hudData.enableEcs = data.enableEcs
    end
    if data.sortingOrder then
        hudData.sortingOrder = data.sortingOrder
    end
    sand_hud_base.InitHudData(self, hudData)
    self:InstantiateModelAsync(data.resPath, data.parent);
end

---实例化成功 （现必须基成设置名字和组件Id）
---@see override
function GWCompPlayerInfoHud:InstantiateSuccess(_obj)
    sand_hud_base.InstantiateSuccess(self, _obj)
    self.memberFaceParent = {}
    for i = 1, 6 do
        if i ~= 3 then
            local faceRoot = UIUtil.GetTrans(self.obj_massTeam.transform, tostring(i))
            self.memberFaceParent[i] = faceRoot
        end
    end

    self:SetPlayerInfoVisible()
    self:SetName()
    self:SetCaptainFaceItem()
    self:SetMemberFaceItem()
end

function GWCompPlayerInfoHud:OnLoaded()
    sand_hud_base.OnLoaded(self)
    self.transform = self.hudData.enableEcs and self.hybridTrans or self.gameObject.transform
    self.obj_singleTeam = self.transform:Find("obj_singleTeam").gameObject
    self.obj_faceNode = self.transform:Find("obj_singleTeam/obj_captain/obj_captainFace/faceNode").gameObject
    self.obj_massTeam = self.transform:Find("obj_massTeam").gameObject
    self.txt_name = self.transform:Find("obj_singleTeam/obj_captain/txtBg/txt_name"):GetComponent(typeof(TextMesh))
    self.txt_renderer = self.transform:Find("obj_singleTeam/obj_captain/txtBg/txt_name"):GetComponent(typeof(MeshRenderer))
    self.txt_name_outline = self.transform:Find("obj_singleTeam/obj_captain/txtBg/txt_name"):GetComponent(typeof(SimpleTextOutline))
    if self.hudData and self.hudData.enableEcs then
        local oriTxtBg = SandEntityBase.GetPrefabSpriteRenderer(self.path).transform:Find("obj_singleTeam/obj_captain/txtBg")
        self.spriteRenderer = oriTxtBg:GetComponent(typeof(SpriteRenderer))
        self.entityNameTab = self.entityNameTab or EntityHybridUtility.GetEntityChildNameIndex(self.hybridEntity)
        self.txt_name_bg =  EntityHybridUtility.GetChild(self.hybridEntity, self.entityNameTab["txtBg"])
        self.captainFace = EntityHybridUtility.GetChild(self.hybridEntity, self.entityNameTab["obj_captainFace"])
        self.obj_lose = EntityHybridUtility.GetChild(self.hybridEntity, self.entityNameTab["obj_lose"])
        self.obj_loseImage = EntityHybridUtility.GetChild(self.hybridEntity, self.entityNameTab["Image"])
    else
        self.obj_captain = self.gameObject.transform:Find("obj_singleTeam/obj_captain").gameObject
        self.obj_lose = self.gameObject.transform:Find("obj_singleTeam/obj_lose").gameObject
        self.txt_name_bg = self.gameObject.transform:Find("obj_singleTeam/obj_captain/txtBg"):GetComponent(typeof(SpriteFitSize))
    end
end

function GWCompPlayerInfoHud:OnUpdateData(serData)
    sand_hud_base.OnUpdateData(self, serData)
    if self.isLoaded then
        self:SetName()
        self:SetPlayerInfoVisible()
    end
end

function GWCompPlayerInfoHud:OnSimpleLevelChanged(level)
    -- 只在集结的时候处理头像逻辑
    if self.isLoaded and self.memberFaceParent then
        self:SetPlayerInfoVisible(level)
    end
end

function GWCompPlayerInfoHud:SetCaptainVisible(visible)
    if self.hudData.enableEcs then
        EntityHybridUtility.SetActive(self.txt_name_bg,visible)
        EntityHybridUtility.SetActive(self.captainFace,visible)
        if entity_manager.IsHybridValid(self.captainFaceItem) then
            EntityHybridUtility.SetActive(self.captainFaceItem,visible)
        end
    else
        UIUtil.SetActive(self.obj_captain, visible)
    end
end

function GWCompPlayerInfoHud:SetLoseVisible(visible)
    if self.hudData.enableEcs then
        EntityHybridUtility.SetActive(self.obj_lose,visible)
        EntityHybridUtility.SetActive(self.obj_loseImage,visible)
    else
        UIUtil.SetActive(self.obj_lose, visible)
    end
end

function GWCompPlayerInfoHud:SetMassTeamVisible(visible)
    if self.hudData.enableEcs then
        if self.memberFaceTable then
            for k, v in pairs(self.memberFaceTable) do
                if v then
                    if visible then
                        v:OnShow()
                    else
                        v:OnHide()
                    end
                end
            end
        end
    else
        UIUtil.SetActive(self.obj_massTeam, visible)
    end
end

function GWCompPlayerInfoHud:SetPlayerInfoVisible(level)
    local marchType = self.serData:GetMarchType()
    if marchType ~= ESMapResMarchKey.March and marchType ~= ESMapResMarchKey.Gather then
        self:SetLoseVisible(false)
        self:SetMassTeamVisible(false)
        self:SetCaptainVisible(false)
        return
    end

    if self.serData.state == common_pb.enSandboxTeamState_Backing and self.serData.props.battleState == sandbox_pb.enSandboxMarchBattleState_Lose then
        self:SetLoseVisible(true)
        self:SetMassTeamVisible(false)
        self:SetCaptainVisible(false)
        self:SetCaptainParent(self.obj_singleTeam.transform)
        return
    end

    self:SetLoseVisible(false)
    self:SetCaptainVisible(true)
    level = level or gw_sand_simple_model_util.GetSimpleLevelState()
    if marchType == ESMapResMarchKey.Gather and self.serData.state == common_pb.enSandboxTeamState_Going and level == 0 then
        self:SetMassTeamVisible(true)
        self:SetCaptainParent(self.memberFaceParent[1])
    else
        self:SetMassTeamVisible(false)
        self:SetCaptainParent(self.obj_singleTeam.transform)
    end
end

function GWCompPlayerInfoHud:SetCaptainParent(parent)
    if util.IsObjNull(parent) or self.parent == parent then
        return
    end
    self.parent = parent
    if self.hudData.enableEcs then
        EntityHybridUtility.AttachToTransform(self.txt_name_bg,parent)
        EntityHybridUtility.AttachToTransform(self.captainFace,parent)
        if entity_manager.IsHybridValid(self.captainFaceItem) then
            EntityHybridUtility.AttachToTransform(self.captainFaceItem,parent)
        end
    else
        UIUtil.SetParent(self.obj_captain.transform, parent)
        UIUtil.ResetTransform(self.obj_captain.transform)
    end
end

function GWCompPlayerInfoHud:SetName()
    self.txt_name.text = self.serData:GetUnionShortAndRoleNameAndServerID()
    self.txt_name.color = self.serData:GetColorType()
    self.txt_name_outline:ManualUpdateOutline()
    if self.hudData.enableEcs then
        self:UpdateSpriteFitDynamicScaleX()
    else
        self.txt_name_bg:UpdateBackground()
    end
end

function GWCompPlayerInfoHud:UpdateSpriteFitDynamicScaleX()
    local spriteRenderer = self.spriteRenderer
    if not (self.txt_renderer and spriteRenderer and spriteRenderer.sprite) then return end
    local sprite = spriteRenderer.sprite
    local spriteWidth = sprite.rect.size.x / sprite.pixelsPerUnit
    local textBounds = self.txt_renderer.bounds
    local targetWidth = textBounds.size.x / self.spriteRenderer.transform.lossyScale.x + 0.5
    local scaleX = targetWidth / spriteWidth
    EntityHybridUtility.SetSpriteFitDynamicScaleX(self.txt_name_bg, scaleX)
end

function GWCompPlayerInfoHud:SetCaptainFaceItem()
    local marchType = self.serData:GetMarchType()
    if marchType == ESMapResMarchKey.March or marchType == ESMapResMarchKey.Gather then
        self.captainFaceItem = self.captainFaceItem or sand_face_item:CFaceItem():Init(self.obj_faceNode.transform)
        local faceId = self.serData:GetFaceId()
        local faceStr = self.serData:GetFaceStr()
        local faceKey = string.IsNullOrEmpty(faceStr) and faceId or faceStr
        self.captainFaceItem:SetFaceKey(faceKey)
        self.captainFaceItem:SetFrameId(self.serData:GetFrameId())
    end
end

function GWCompPlayerInfoHud:SetMemberFaceItem()
    local marchType = self.serData:GetMarchType()
    if marchType == ESMapResMarchKey.Gather and self.serData.state == common_pb.enSandboxTeamState_Going and not self.memberFaceTable then
        self.memberFaceParent[1] = UIUtil.GetTrans(self.obj_massTeam.transform, "1")
        local member = self.serData:GetMemberTeam()
        if member then
            self.memberFaceTable = {}
            --第一个位置是队长的,动态设置
            for i = 1, #member do
                local index = i > 1 and (i + 2) or (i + 1)
                if not util.IsObjNull(self.memberFaceParent[index]) then
                    self.memberFaceTable[i] = sand_face_item:CFaceItem():Init(self.memberFaceParent[index])
                    local faceInfo = member[i]
                    local faceKey = string.IsNullOrEmpty(faceInfo.FaceStr) and faceInfo.FaceID or faceInfo.FaceStr
                    self.memberFaceTable[i]:SetFaceKey(faceKey)
                    self.memberFaceTable[i]:SetFrameId(faceInfo.FrameID)
                end
            end
        end
    end
end

function GWCompPlayerInfoHud:OnShow(bool)
    if self.hudData.enableEcs and self.isLoaded then
        self:SetPlayerInfoVisible()
        if bool then
            if self.captainFaceItem then
                self.captainFaceItem:OnShow()
            end
        else
            if self.captainFaceItem then
                self.captainFaceItem:OnHide()
            end
        end
    end
    sand_hud_base.OnShow(self,bool)
end

function GWCompPlayerInfoHud:Dispose(unloadShowOnly)
    if self.captainFaceItem then
        self.captainFaceItem:Dispose()
        self.captainFaceItem = nil
    end

    if self.memberFaceTable then
        for k, v in pairs(self.memberFaceTable) do
            if v then
                v:Dispose()
                v = nil
            end
        end
        self.memberFaceTable = nil
    end
    self.memberFaceParent = nil

    if self.isLoaded then
        -- 还原状态
        self:SetCaptainParent(self.obj_singleTeam.transform)
    end
    sand_hud_base.Dispose(self, unloadShowOnly)
end

return GWCompPlayerInfoHud