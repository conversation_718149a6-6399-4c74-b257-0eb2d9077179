---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/13 11:00
--- Desc: HUD升级 提示
local require = require
local ipairs = ipairs
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local game_scheme = require "game_scheme"
local GWConst = require "gw_const"
local util = require "util"
local time_util = require "time_util"
local gw_home_comp_hud_base = require "gw_home_comp_hud_base"
local LeanTween = CS.LeanTween
local gw_home_building_data = require "gw_home_building_data"
local gw_home_survivor_data = require "gw_home_survivor_data"
local card_assets = require "card_sprite_asset"
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWHUDEventUpgradeObject : unit_base HUD基础对象
---@field __base GWHUDFightObject
module("gw_home_comp_hud_upgrade")
local GWHUDEventUpgradeObject = newclass("gw_home_comp_hud_upgrade", gw_home_comp_hud_base)
local yOffset = 0
--- 构造器
function GWHUDEventUpgradeObject:ctor()
    gw_home_comp_hud_base.ctor(self)
end

---@public 设置数据 （现必须基成设置数据）
---@param bindParent table Transform 绑定的父节点
---@see override
function GWHUDEventUpgradeObject:InitData(bindParent, data)
    gw_home_comp_hud_base.InitData(self, bindParent, data)
    self:InstantiateModelAsync("art/greatworld/home/<USER>/hud/gwhomeupgradehud.prefab", data.parent)
end

--- @public 基类方法实例化模型
---@param path string 模型路径
---@param parent table Transform 父节点 (不设置 默认为管理的父物体节点)
---@see override
function GWHUDEventUpgradeObject:InstantiateModelAsync(path, parent)
    gw_home_comp_hud_base.InstantiateModelAsync(self, path, parent)
end

---实例化成功 （现必须基成设置名字和组件Id）
---@param _obj table GameObject 模型对象
---@see override
function GWHUDEventUpgradeObject:InstantiateSuccess(_obj)
    self.parentTr = UIUtil.GetComponent(_obj.transform, "RectTransform", "parent")
    self.infoText = UIUtil.GetComponent(_obj.transform, "Text", "parent/Text")
    self.Light = UIUtil.GetComponent(_obj.transform, "RectTransform", "parent/Light")
    self.iconListComList = UIUtil.GetComponentList("Image", _obj.transform, "parent/IconList")
    gw_home_comp_hud_base.InstantiateSuccess(self, _obj)
    local buildId = gw_home_building_data.GetBuildingIdByBuildingType(GWConst.enBuildingType.enBuildingType_WorkerHouse)
    local data = gw_home_building_data.GetMinLevelBuildingDataByBuildingID(buildId)
    local survivorList = gw_home_survivor_data.GetSurvivorListByBuildingSid(data.uSid)
    self:SetLightShow(survivorList)
    self:SetSurvivorIcon(survivorList)
    LeanTween.cancel(self.parentTr.gameObject) --取消之前的动画
    LeanTween.moveY(self.parentTr, 30, 0.5)
end
---@public 设置幸存者图标
function GWHUDEventUpgradeObject:SetSurvivorIcon(survivorList)
    if self.iconListComList == nil then
        return
    end
    self.spriteAsset = card_assets.CreateCardSpriteAsset("survivorHeadAsset")
    local iconListCount = self.iconListComList.Count
    for i = 0, iconListCount - 1 do
        local survivorData = survivorList[i + 1]
        local bgImage = self.iconListComList[i]
        if survivorData then
            local survivorCfg = game_scheme:BuildSurvivor_0(survivorData.nID)
            if survivorCfg then
                self.spriteAsset:GetSprite(survivorCfg.faceID, function(sprite)
                    if not util.IsObjNull(bgImage) and not util.IsObjNull(sprite) then
                        bgImage.sprite = sprite
                    end
                end)    
            end            
            UIUtil.SetActive(bgImage, true)
        else
            UIUtil.SetActive(bgImage, false)
        end
    end
end

---@public 设置光亮显示
function GWHUDEventUpgradeObject:SetLightShow(survivorList)
    if util.IsObjNull(self.Light) then
        return
    end
    if survivorList and util.get_len(survivorList) > 0 then
        UIUtil.SetActive(self.Light, true)
    else
        UIUtil.SetActive(self.Light, false)
    end
end

function GWHUDEventUpgradeObject:SetText(time)
    self:AddLoadEvent(function()
        self.infoText.text = time_util.FormatTimeXMan(time)
    end)
end

function GWHUDEventUpgradeObject:SetOffsetScale(scale)
    self.offset_y = yOffset * scale
end

---清楚基类数据
---@see override
function GWHUDEventUpgradeObject:ClearData()
    if self.spriteAsset then
        self.spriteAsset:Dispose()
        self.spriteAsset = nil
    end
    if not util.IsObjNull(self.parentTr) then
        LeanTween.cancel(self.parentTr.gameObject) --取消之前的动画
        UIUtil.SetAncoPosition(self.parentTr, 0, 0)
    end
    self.iconListCom = nil
    gw_home_comp_hud_base.ClearData(self)
    self.offset_x = 0                 --偏移x
    self.offset_y = yOffset           --偏移y
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWHUDEventUpgradeObject
