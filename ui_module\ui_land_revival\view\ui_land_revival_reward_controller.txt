local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local event_task_define = require "event_task_define"
local gw_task_const = require "gw_task_const"
local gw_task_mgr = require "gw_task_mgr"
local gw_vip_data = require "gw_vip_data"
local gw_task_data = require "gw_task_data"
local land_revival_data = require "land_revival_data"
local game_scheme = require "game_scheme"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_land_revival_reward_controller")
local controller = nil
local UIController = newClass("ui_land_revival_reward_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)

    self:InitRewardViewData()
    self:GetActivityEndTime()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.taskUpdate = function(eventName, taskData, moduleId, moduleList)
        if moduleList[gw_task_const.TaskModuleType.pioneer_target] then
            self:InitRewardViewData()
        end
    end
    self:RegisterEvent(event_task_define.REFRESH_TASK, self.taskUpdate)
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end
function UIController:OnBtnVipClickedProxy()
    ui_window_mgr:ShowModule("ui_vip_main_new")
end
function UIController:OnSliderFirstProgressValueChange(value)
end
function UIController:OnBtnReceiveClickedProxy()
    self:OneClickReceiveTask()
end

---@public function 获取奖励数据
function UIController:InitRewardViewData()
    local vipTaskData = land_revival_data.GetRevivalTaskData()
    if vipTaskData then
        self.selfVipLevel = gw_vip_data.GetVipLevel()
        self:TriggerUIEvent("SetVipLevelShow", self.selfVipLevel)

        local viewData = {}
        local len = #vipTaskData
        for i, v in ipairs(vipTaskData) do
            local itemData = {}
            local normalData = self:CreateTaskData(v.taskID)
            local vipData = self:CreateTaskData(v.vipTaskID)
            itemData.normalData = normalData
            itemData.vipData = vipData
            itemData.vipLevel = v.vipLevel
            itemData.isLockVip = self.selfVipLevel < v.vipLevel
            if i == len then
                itemData.isLastTask = true
            end
            table.insert(viewData, itemData)
        end

        for i, v in ipairs(viewData) do
            if self:JudgeTaskIsFinish(v.normalData) then
                local preViewData = viewData[i - 1]
                if preViewData then
                    preViewData.isFinish = true
                else
                    --让特殊位置为1
                    self:TriggerUIEvent("SetFirstTaskProgress", 1)
                end

            end
        end

        self.curTaskData = viewData --当前处理好的任务数据
        --生成界面数据
        self:TriggerUIEvent("RefreshRewardList", viewData)
    end
end

---@public function 创建任务数据
function UIController:CreateTaskData(taskID)
    local taskCfg = game_scheme:TaskMain_0(taskID)
    local taskData = gw_task_data.GetTaskData(taskID)
    if taskData and taskCfg then
        local tempData = {
            taskID = taskID,
            taskLang = taskCfg.TaskLang,
            rewardID = taskCfg.TaskReward,
            targetRate = taskCfg.ConditionValue1,
            heroID = taskCfg.ConditionValue2.data[0],
            status = taskData.status,
            rate = taskData.rate,
        }
        return tempData
    end
    return nil
end

---@public function 判断任务是否完成
function UIController:JudgeTaskIsFinish(taskData)
    if taskData.status then
        --已完成
        return true
    else
        if taskData.rate >= taskData.targetRate then
            --已完成
            return true
        else
            --未完成
            return false
        end
    end
end

function UIController:GetActivityEndTime()
    local activityData = land_revival_data.GetActivityData()
    if activityData then
        local endTime = activityData.endTimeStamp
        self:TriggerUIEvent("SetActivityTimer", endTime)
    end
end

---@public function 一键领取任务
function UIController:OneClickReceiveTask()
    --local vipTaskData = land_revival_data.GetRevivalTaskData()
    --还是用处理过的数据吧
    if self.curTaskData then
        local canReceiveTaskArr = {}
        for _, v in ipairs(self.curTaskData) do
            if not v.normalData.status and v.normalData.rate >= v.normalData.targetRate then
                --可领取
                table.insert(canReceiveTaskArr, v.normalData.taskID)
            end
            if not v.vipData.status and v.vipData.rate >= v.vipData.targetRate then
                if not v.isLockVip then
                    --可领取
                    table.insert(canReceiveTaskArr, v.vipData.taskID)
                end
            end
        end
        gw_task_mgr.ReceiveTaskListReward(canReceiveTaskArr, land_revival_data.GetActivityTaskID(), nil)
    end
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
