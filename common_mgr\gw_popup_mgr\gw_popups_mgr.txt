-- gw_popups_mgr.txt ------------------------------------------
-- author:  梁骐显
-- date:    2020.2.17
-- ver:     1.0
-- desc:   	登陆弹窗管理器
--------------------------------------------------------------

local require = require
local table = table
local pairs = pairs
local ipairs = ipairs
local math = math
local print = print
local tonumber = tonumber
local string = string
local os = os
local xpcall = xpcall
-- local loadstring = loadstring or load -- "load" replaced "loadstring" in Lua 5.2
local files_version_mgr = require "files_version_mgr"
local ReviewingUtil = require "ReviewingUtil"
local time_util = require "time_util"
local ui_window_mgr = require "ui_window_mgr"
local game_scheme = require "game_scheme"
local event = require "event"
local player_mgr = require "player_mgr"
local log = require "log"
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local gw_popups_data = require "gw_popups_data"
local event_define = require "event_define"
local gw_popup_config = require "gw_popup_config"
local gw_popups_trigger_mgr = require "gw_popups_trigger_mgr"
local util = require "util"
module("gw_popups_mgr")
logger = require("logger").new("gw_popups_mgr", 0)
Warning = logger.Warning

local LoginPopupEnum = gw_popup_config.LoginPopupEnum
local isLoginTest = false --测试开关，打开之后会弹出所有弹窗
local isNotCheckTime = false --测试开关，打开之后，不计算冷却CD
local isCloseLoginPop = true --测试开关，true 所有登录弹窗都不弹

--不展示的条件
--（1=购买某个礼包后、2=某个累充活动的所有奖励领取完后（活动ID）、3=某个累充活动重置达到X（活动ID#充值金额）、4=通关第X关（关卡ID）、5=月卡持续期间）
StopType = {
    BuyGift = 1,
    TotalRecharge_GetReward = 2,
    TotalRecharge_Recharge = 3,
    PassLevel = 4,
    MonthCard = 5,
}

--登录弹出条件,满足所有才弹（1=购买某个礼包后、2=月卡持续期间）
StartType = {
    BuyGift = 1,
    MonthCard = 2,
}

CDType = {
    LoginMust = 1,--登录必须的
    LoginPriority = 2,--登录顺序的
    LoginRandom = 3,--登录随机的
    Trigger = 4,--关卡等触发的
}

--初始化配置

--权重顺序，比如游戏失败后根据权重弹一个礼包
--local cfgPriorityList = {}
local cfgPriorityMap = {}
local popupWndConfigMap = {}

--初始化

local funcEnterGame = function()
    if isLoginTest then
        for popindex,v in pairs(popupWndConfigMap) do
            if CheckPop(popindex,CDType.LoginPriority) then
                local config = popupWndConfigMap[popindex]
                EnPopup(popindex,CDType.LoginPriority,config)
            end
        end
        return
    end
    InitLoginPop()
end

function Init()
    --初始化配置
    event.Register(event_define.FIRST_LOGIN_CREATE_DATA_FINISH, funcEnterGame)
end

--初始化配置，和角色信息无关
function InitPopCfg()
    Warning(4,"步骤1 InitPopCfg")
    local conditionType2Id = {}

    local num = game_scheme:PopupControl_nums()
    for i = 1, num do
        local cfg = game_scheme:PopupControl(i)
        if cfg then
            local conditionTypes = string.split(cfg.conditionType, ";",tonumber)
            local conditionValues = string.split(cfg.conditionValue, ";")
            local value
            for i,type in ipairs(conditionTypes or {}) do
                value = cfg.PopupType
                if conditionValues and conditionValues[i] then
                    value = conditionValues[i]
                end
                if not conditionType2Id[type] then
                    conditionType2Id[type] = {}
                end
                conditionType2Id[type][value] = cfg.PopupType
            end
            -- local data = {
            --     id = cfg.PopupType,
            --     index = i,
            --     Priority = cfg.Priority
            -- }
            --table.insert(cfgPriorityList, data)
            cfgPriorityMap[cfg.PopupType] = cfg.Priority
        end
    end
    -- if cfgPriorityList[2] then
    --     table.sort(cfgPriorityList, function(a, b)
    --         if a.Priority == b.Priority then
    --             return a.index < b.index
    --         else
    --             return a.Priority < b.Priority
    --         end
    --     end)
    -- end
    --用于缓存 活动和弹窗的关系
    gw_popups_trigger_mgr.SetConditionType(conditionType2Id)
end

local pop2ActiveId = nil
function InitActivePop()
    pop2ActiveId = {}
    local len = game_scheme:ActivityMain_nums()
    for i = 0, len - 1 do
        local mainCfg = game_scheme:ActivityMain(i)
        if mainCfg and mainCfg.uiTemplateID and mainCfg.uiTemplateID>0 then
            local cfg = game_scheme:ActivityCommonUI_0(mainCfg.uiTemplateID)
            if cfg and cfg.CustomParam1 then
                local CustomParam1 = tonumber(cfg.CustomParam1)
                if CustomParam1 and cfgPriorityMap[CustomParam1] and not pop2ActiveId[CustomParam1] then
                    --说明这些活动是要弹窗的
                    pop2ActiveId[CustomParam1] = mainCfg.AtyID
                end
            end
        end
        
    end
end
function GetActiveIdByPop(popupIndex)
    if not pop2ActiveId then
        --用于缓存 活动和弹窗的关系
        InitActivePop()
    end
    return pop2ActiveId[popupIndex]
end

local isFirstLogin = false --为了只初始化一次
local loginPopIndexMap = {}--为了记录登录index第几个
local loginPopIndexMax = 0
local randomCD = 0

local loginEnPopupList = {}
function LoginEnPopup(popupIndex,triggerType,config)
    table.insert(loginEnPopupList,{
        popupIndex = popupIndex,
        triggerType = triggerType,
        config = config
    })
end

--初始化登录，跟人物数据有关
function InitLoginPop()

    if isCloseLoginPop then
        return
    end
    
    Warning(4,"步骤2 InitLoginPop")
    if isFirstLogin then
        return
    end
    InitPopCfg()
    local gw_popup_config = require "gw_popup_config"
    popupWndConfigMap = gw_popup_config.GetPopupWindowListConfig()
    isFirstLogin = true
    loginEnPopupList = {}
    --注册
    gw_popups_trigger_mgr.InitRegister()
    local gw_home_novice_util = require "gw_home_novice_util"
    local isNotNovice = gw_home_novice_util.IsGuideIng()
    if isNotNovice then
        --如果新手引导期间，不弹
        --LoginPopEnd()
        return
    end
    local key = string.format("%dLoginPopupPlan", player_mgr.GetPlayerRoleID())
    local planID = PlayerPrefs.GetInt(key)
    ------  --print("planID=",planID,planID==0,planID=="",planID==nil)
    math.randomseed(os.time())
    if planID == 0 then
        -- 随机选择弹窗方案，决定各种弹窗优先级，次数，冷却时间等数据
        local count = game_scheme:LoginPopup_nums()
        if not count or count <= 0 then
            --log.Error("LoginPopup打表问题")
        end
        local value = count and count > 0 and math.random(count)
        PlayerPrefs.SetInt(key, value)
        planID = value
    end

    GameReport(planID)
    
    local planCfg = planID and game_scheme:LoginPopup_0(planID)
    if not planCfg then
        planCfg = game_scheme:LoginPopup_0(1)
    end
    if  planCfg then
        --必弹弹窗

        --队列弹窗
        Warning(4,"步骤3 InitLoginPop 队列弹窗")
        local planPriorityArr = planCfg and planCfg.Priority.data
        local planPriorityNum = planCfg and planCfg.Priority.count
        randomCD = planCfg.RandomCD or 0
        local MaxPopup = planCfg and planCfg.MaxNum or 999
        local addCount = 0
        if planPriorityNum and planPriorityArr then
            -- 按 planCfg.Priority 显示顺序处理弹窗类型，保证弹窗顺序
            local loginKey = GetLognIndexKey()
            local loginIndex = PlayerPrefs.GetInt(loginKey, 0)
            --小于1或者大于配置长度，都从1开始
            if not loginIndex or loginIndex<1 or loginIndex>planPriorityNum then
                loginIndex = 0
            end
            --记录是上次谈过的，这个从下一个开始
            loginIndex = loginIndex + 1
            loginPopIndexMax = planPriorityNum
            local loginEndIndex = loginIndex
            for i = loginIndex, planPriorityNum do
                if MaxPopup and addCount >= MaxPopup then
                    break
                end
                local popindex = planPriorityArr[i - 1]
                if not popindex then
                    --print("<color=#FF0000>弹窗数据加载完毕，本角色弹窗方案:"..planID.."，优先级"..i.."为空，跳过>>>>>></color>")
                    break
                end
                loginPopIndexMap[popindex] = i
                local isOpen,config = CheckPop(popindex,CDType.LoginPriority)
                if isOpen then
                    LoginEnPopup(popindex,CDType.LoginPriority,config)
                    addCount = addCount + 1
                end
                loginEndIndex = i
            end
            Warning(4,"LoginPrioritys开始位置 = ",loginIndex," 结束位置 = ",loginEndIndex)
            --如果后面的没有一个，则下次从1开始
            if addCount<1 then
                Warning(4,"LoginPrioritys 循环一圈，下次从1开始")
                PlayerPrefs.SetInt(loginKey, 0)
            end 
        end
    end
    
    Warning(4,"步骤4 InitLoginPop 必弹弹窗")
    local planMustArr = planCfg and planCfg.MustPopup.data
    local planMustNum = planCfg and planCfg.MustPopup.count
    if planMustArr and planMustNum then
        for i=1,planMustNum do
            local popindex = planMustArr[i - 1]
            local isOpen,config = CheckPop(popindex,CDType.LoginMust)
            if isOpen then
                LoginEnPopup(popindex,CDType.LoginMust,config)
            end
        end
    end

    Warning(4,"步骤5 InitLoginPop 随机弹窗")
    --添加随机的
    if planCfg then
        local openList = {}
        local count = planCfg.RandomPlan.count
        --选出来开放的
        for i=1,count do
            local popindex = planCfg.RandomPlan.data[i-1]
            if popindex then
                local isOpen = CheckPop(popindex,CDType.LoginRandom)
                if isOpen then
                    table.insert(openList,popindex)
                end 
            end
        end
        --随机个数,添加到randomAddList
        
        --local randomAddList = {}
        for i = 1, planCfg.RandomNum do
            if openList[1] then
                local index = math.random(1,#openList)
                local popindex = openList[index]
                table.remove(openList,index)
                --table.insert(randomAddList,popindex)
                local config = popupWndConfigMap[popindex]
                LoginEnPopup(popindex,CDType.LoginRandom,config)
            else
                break
            end
        end
        -- --根据权重，优先插入权重高的
        -- table.sort(randomAddList,function(a,b)
        --     local PriorityA = cfgPriorityMap[a]
        --     local PriorityB = cfgPriorityMap[b]
        --     return PriorityA > PriorityB
        -- end)
        -- for i,popindex in ipairs(randomAddList or {}) do
        --     EnPopup(popindex,CDType.LoginRandom,config)
        -- end
    end
    
    local customList = gw_popup_config.GetCustomPopupWindowListConfig()
    for i,v in pairs(customList) do
        local isOpen = v.isShowFun()
        if isOpen then
            LoginEnPopup(i,CDType.LoginMust,v)
        end
    end
    
    if loginEnPopupList[1] then
        local count = #loginEnPopupList
        for i,v in ipairs(loginEnPopupList) do
            if i == count then
                EnPopup(v.popupIndex,v.triggerType,v.config,function()
                    LoginPopEnd()
                end)
            else
                EnPopup(v.popupIndex,v.triggerType,v.config)
            end
        end
    else
        --没有登录弹窗
        LoginPopEnd()
    end
end
function LoginPopEnd()
    --log.Error("登录弹窗结束")
    event.Trigger(event.LOGIN_UI_POPUP_END)
end

function EnPopup(popupIndex,triggerType,config,endFunc)
    local gw_home_novice_util = require "gw_home_novice_util"
    local isNotNovice = gw_home_novice_util.IsGuideIng()
    if isNotNovice then
        --如果新手引导期间，不弹
        Warning(4,"EnPopup 新手引导期间,登录弹窗不弹 popupIndex = ",popupIndex)
        return
    end
    if popupIndex and triggerType and config then
        local key = GetPopKey(triggerType,popupIndex)
        local cfg = game_scheme:PopupControl_0(popupIndex)
        if not cfg then
            log.Error("EnPopup 弹窗数据不存在 popupIndex = ",popupIndex)
            --return
        end
        Warning(4,"添加弹窗key = ",key)
        local Priority = 0
        if cfg and triggerType == CDType.Trigger then
            Priority = cfg.Priority
        end
        gw_popups_data.AddMessage(key,function()
            --再次检测开放,有些和时间有关系的怕过期掉
            local isOpen = config.isShowFun and config.isShowFun(popupIndex) or true
            --自动化验区
            local isAutoSwitch = files_version_mgr.GetAutoMaticZoneSwitching()
            --判断开启 审核服和非审核服
            if isOpen and (not ReviewingUtil.IsReviewing() or gw_popup_config.PopupNotShowReviewing[config.PopupType]) and not isAutoSwitch then
                local function closeFun()
                    --显示下一个弹窗
                    -- local first = gw_popups_data.GetFirstMessage()
                    -- if first and first.key == key then
                    --     gw_popups_data.ShowNextMessage(0)
                    -- else
                    --     Warning(4,"界面关闭没有移除 key =",key)
                    -- end
                    if popupIndex == LoginPopupEnum.BomberMan then
                    else
                        gw_popups_data.ShowNextMessage(0)
                    end
                    if endFunc then
                        endFunc()
                    end
                end
                local data = config.uiData and config.uiData(popupIndex) or {}
                --用于判断对象界面是不是在队列中
                data.isAddMessage = true
                data.messageKey = key
                ui_window_mgr:ShowModule(config.uiName, nil, closeFun, data)
                SetShowTime(key)
            else
                gw_popups_data.ShowNextMessage()
            end
        end,gw_popups_data.PopupsType.Popups,0.5,Priority)
    end
end


function CheckCommonCondition(popupIndex,triggerType)
    local cfg = game_scheme:PopupControl_0(popupIndex)
    if not cfg then
        return nil
    end
    local isOpen = true
    isOpen = CheckStartCondition(popupIndex)
    if  isOpen and triggerType then
        --检测时间
        if triggerType ~= CDType.LoginPriority then
            local key = GetPopKey(triggerType,popupIndex)
            if not CheckShowTime(key) then
                isOpen = false
            end
        end
    end
    --检测关闭条件
    if isOpen then
        local isStop = CheckStopCondition(popupIndex)
        if isStop then
            isOpen = false
        end
    end
    return isOpen
end

function CheckStartCondition(popupIndex,triggerType)
    local isStart = true
    local cfg = game_scheme:PopupControl_0(popupIndex)
    if not cfg then
        return nil
    end
    if cfg.Startcondition and cfg.StartconditionValue then
        local startTypeList = string.split(cfg.Startcondition, ";")
        local startValueList = string.split(cfg.StartconditionValue, ";")
        if startTypeList and startValueList then
            for i,v in ipairs(startTypeList or {}) do
                if startValueList[i] then
                    isStart = CheckStartConditionOne(startTypeList[i],startValueList[i])
                    if not isStart then
                        break
                    end
                end
            end
        end
    end
    return isStart
end

--（1=购买某个礼包后、2==月卡持续期间）
function CheckStartConditionOne(type,value)
    local isStart = true
    local type = tonumber(type)
    if type == StartType.BuyGift then
        --购买某个礼包后
        local rechargeId = tonumber(value)
        local gw_recharge_mgr = require "gw_recharge_mgr"
        local buyNum = gw_recharge_mgr.GetRechargeBuyCount(rechargeId)
        isStart = buyNum>0
    elseif type == StartType.MonthCard then
        --月卡持续期间
        local month_card_data = require "month_card_data"
        isStart = month_card_data.IsMonthCardValid()
    end
    return isStart
end

function CheckStopCondition(popupIndex)
    local isStop = false
    local cfg = game_scheme:PopupControl_0(popupIndex)
    if not cfg then
        return nil
    end
    local stopTypeList = string.split(cfg.Stopcondition, ";")
    local stopValueList = string.split(cfg.StopconditionValue, ";")
    if stopTypeList and stopValueList then
        for i,v in ipairs(stopTypeList or {}) do
            if stopValueList[i] then
                isStop = CheckStopConditionOne(stopTypeList[i],stopValueList[i])
                if isStop then
                    break
                end
            end
        end
    end
    return isStop
end
--（1=购买某个礼包后、2=某个累充活动的所有奖励领取完后（活动ID）、3=某个累充活动重置达到X（活动ID#充值金额）、4=通关第X关（关卡ID）、5=月卡持续期间）
function CheckStopConditionOne(type,value)
    local isStop = false
    local type = tonumber(type)
    if type == StopType.BuyGift then
        --购买某个礼包后
        local rechargeId = tonumber(value)
        local gw_recharge_mgr = require "gw_recharge_mgr"
        local buyNum = gw_recharge_mgr.GetRechargeBuyCount(rechargeId)
        isStop = buyNum>0
        -- local serverData = gw_recharge_mgr.GetRechargeServerData(rechargeId)
        -- if serverData then
        --     if not serverData.isBuy then
        --         local buyNum = gw_recharge_mgr.GetRechargeBuyCount(rechargeId)
        --         isStop = buyNum>0
        --     end
        -- end 
    elseif type == StopType.TotalRecharge_GetReward then
        --某个累充活动的所有奖励领取完后
        local activityId = tonumber(value)
        local gw_totalrecharge_mgr = require "gw_totalrecharge_mgr"
        isStop = gw_totalrecharge_mgr.CheckTotalRecharge_AllGet(activityId)
    elseif type == StopType.TotalRecharge_Recharge then
        --某个累充活动重置达到X
        local list = string.split(value, "#", tonumber)
        if list[1] and list[2] then
            local gw_totalrecharge_mgr = require "gw_totalrecharge_mgr"
            isStop = gw_totalrecharge_mgr.CheckTotalRecharg_IsRecharge(list[1],list[2])
        end 
    elseif type == StopType.PassLevel then
        --通关第X关
        local level = tonumber(value)
        local laymain_data = require "laymain_data"
        local curLevel = laymain_data.GetPassLevel()
        if  level and curLevel then
            isStop = curLevel>=level
        end
    elseif type == StopType.MonthCard then
        --月卡持续期间
        local month_card_data = require "month_card_data"
        isStop = month_card_data.IsMonthCardValid()
    end
    return isStop
end

function CheckDetailCondition(popupIndex)
    local config = popupWndConfigMap[popupIndex]
    if config then
        local isOpen = config.isShowFun and config.isShowFun(popupIndex)
        return isOpen,config
    end
    return false,nil
end

--加入队列的唯一key
function GetPopKey(triggerType,popupIndex)
    return string.format("Popup_%d_%d_%d",triggerType,popupIndex,player_mgr.GetPlayerRoleID())
end

--登录记录上次谈到第几个的index
function GetLognIndexKey()
    return string.format("PopupLoginIndex_%d",player_mgr.GetPlayerRoleID())
end

--记录展示时间
function SetShowTime(key)
    --登录+随机+条件触发 时间不共享时间次数
    local triggerType,popupIndex,roleId = string.match(key,"Popup_(%d+)_(%d+)_(%d+)")
    triggerType = tonumber(triggerType or 0)
    popupIndex = tonumber(popupIndex or 0)
    if  triggerType and popupIndex then
        if  triggerType == CDType.LoginPriority then
            --登录没有时间cd，只有顺序
            local loginKey = GetLognIndexKey()
            local loginPopIndex = loginPopIndexMap[tonumber(popupIndex)] or 0
            if loginPopIndex and loginPopIndex>= loginPopIndexMax  then
                loginPopIndex = 0
            end
            PlayerPrefs.SetInt(loginKey, loginPopIndex)
            return
        else
            --设置为当前时间
            local timeKey = "timeKey"..key
            local curTime = os.server_time()
            PlayerPrefs.SetInt(timeKey, curTime)
            if triggerType == CDType.Trigger then
                --触发条件，要记录次数
                --叠加次数
                local limitKey = "limitKey"..key
                local limitCount = PlayerPrefs.GetInt(limitKey,0)
                PlayerPrefs.SetInt(limitKey, limitCount+1)
            end
        end
    end
end

--检测时间
function CheckShowTime(key)
    if isNotCheckTime then
        return true
    end
    local triggerType, popupIndex, roleId = string.match(key, "Popup_(%d+)_(%d+)_(%d+)")
    triggerType = tonumber(triggerType or 0)
    popupIndex = tonumber(popupIndex or 0)
    if not (triggerType and popupIndex) then
        return true
    end

    if triggerType == CDType.LoginPriority then
        return true
    end

    local cfg = game_scheme:PopupControl_0(popupIndex)
    if not cfg then
        return true
    end

    local timeKey = "timeKey" .. key
    local lastTime = PlayerPrefs.GetInt(timeKey, 0)
    local lastZeroTime = time_util.GetTimeOfZero(os.server_zone(), lastTime)
    local curTime = os.server_time()
    local zeroTime = time_util.GetTimeOfZero(os.server_zone(), curTime)

    if lastZeroTime == zeroTime then
        local cd = triggerType == CDType.LoginRandom and randomCD or cfg.PopupCD
        if cd and cd>0 then
            if curTime <= lastTime + cd then
                Warning(4,"CheckShowTime 不满足PopupCD条件 key = ",key," lastTime = ",lastTime," cd = ",cd)
                return false
            end
    
            if triggerType == CDType.Trigger then
                local limitKey = "limitKey" .. key
                local limitCount = PlayerPrefs.GetInt(limitKey, 0)
                if limitCount >= cfg.PopupLimit then
                    Warning(4,"CheckShowTime 不满足PopupLimit条件 key = ",key," limitCount = ",limitCount," cfg.PopupLimit = ",cfg.PopupLimit)
                    return false
                end
            end
        end
    end

    return true
end

function CheckPop(popupIndex,triggerType)
    local check, isOpen,config = xpcall(function()
        if CheckCommonCondition(popupIndex,triggerType) then
            return CheckDetailCondition(popupIndex)
        end
    end,function(err)
            log.Error("CheckPop triggerType：", triggerType," popupIndex：",popupIndex, " 错误信息:", err)
        end)
    if check then
        return isOpen,config
    else
        return false,nil
    end
end

function CheckPopByKey(key)
    local triggerType,popupIndex,roleId = string.match(key,"Popup_(%d+)_(%d+)_(%d+)")
    return CheckPop(tonumber(popupIndex),tonumber(triggerType))
end

function GetSplitKey(key)
    local triggerType,popupIndex,roleId = string.match(key,"Popup_(%d+)_(%d+)_(%d+)")
    return triggerType,popupIndex,roleId
end

function GameReport(planID)
    local lang = require "lang"
    local UTCOffset = time_util.GetTimeArea()
    local account_pb = require "account_pb"
    local timeZoneId = account_pb.enTimeZone_meizhou
    if UTCOffset then
        if UTCOffset >= 7 and UTCOffset <= 12 then
            timeZoneId = account_pb.enTimeZone_yatai
        elseif UTCOffset >= -2 and UTCOffset <= 6 then
            timeZoneId = account_pb.enTimeZone_ouzhou
        elseif UTCOffset >= -11 and UTCOffset <= -3  then
            timeZoneId = account_pb.enTimeZone_meizhou
        end
    end
    
    local reportMsg = {
        popup_id = planID,
        language = lang.USE_LANG,
        role_TimeZone = timeZoneId
    }
    event.EventReport("login_record", reportMsg)--屏蔽登录日志
end
--有用户操作的情况下，终止队列
function StopPop(messageKey)
    if messageKey then
        gw_popups_data.RemoveMessageByKey(messageKey)
    end
    local _remainPopMessageList = gw_popups_data.GetMessageByType(gw_popups_data.PopupsType.Popups)
    gw_popups_data.SetRemainPopMessageList(_remainPopMessageList)
    gw_popups_data.RemoveMessageByType(gw_popups_data.PopupsType.Popups)
    util.DelayCallOnce(0.5,function()
        gw_popups_trigger_mgr.AddTriggerFunc(function ()
            gw_popups_data.ShowRemianPopMessageList()
        end )
    end)

end

--TODO清除数据 上下要对应
function Clear()
    isFirstLogin = false
    gw_popups_trigger_mgr.UnRegister()
    gw_popups_data.ClearMessage()
end
event.Register(event.USER_DATA_RESET, Clear)