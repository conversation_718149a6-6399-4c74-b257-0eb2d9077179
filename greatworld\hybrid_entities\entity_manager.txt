-- author:  zqf
-- desc: pure lua table entity + HPC# Job System, no api call overhead
local log = require "log"
local bit = require "bit"
local xpcall = xpcall
local typeof = typeof
local EnityConversionUtility = CS.Unity.Entities.EnityConversionUtility
local EntityHybridUtility

--require "lua_csharp_arr_test"

--todo using // in lua5.4 or luau 
local floor = math.floor
local min = math.min

local EntityManager = {
    On = true,
    Streaming = true,
    StreamingUnload = false,
    CSPointCloudProcessor = true,
    NewSrvData = true,
    MoveOpt = true,
    GpuAnimatorJob = false,
    CSVersion = 0,
    SpriteEcs = true,
    ModelEcs = true,
 	FaceItemEcs = true,
    URP22 = false,
    SandEcs = true,
    VegetationEcs = true,
    EcsDev = false,
    CysoldierssortieEcs = true,
    HomeEcs = true,
}

local val = require "val"
EntityManager.CSPointCloudProcessor = EntityManager.On and EntityManager.CSPointCloudProcessor
EntityManager.NewSrvData = EntityManager.On and EntityManager.NewSrvData
EntityManager.MoveOpt = EntityManager.On and EntityManager.MoveOpt

EntityManager.__index = EntityManager

local entities
local positions
local radiuses
local scales
local stateMasks
local pixelOffsets
local lodRanges
local parentEntities
local entitiesIndexer
local hybridEntities
local rotations 
local entitiesIndexerCount

local loadQueueTable
local unLoadQueueTable
local innerLodChangedTable

--local _entitiesIndexer = {}  
--local _freeIndices
local nextFreeEntityIndex

local transformAccessArray

local entityObjCache = setmetatable({}, { __mode = "vk" })
---------------------------------------------PinTable------------------------------------------------------------------

function isUnityVersionAtLeast(targetMajor)
    local version = CS.UnityEngine.Application.unityVersion or "0.0.0" -- fallback in case it's nil
    local major = tonumber(version:match("^(%d+)%.")) or 0
    return major >= targetMajor
end
local unity2022 = isUnityVersionAtLeast(2022)
local entityGameObj = CS.UnityEngine.GameObject("Entities")
CS.UnityEngine.GameObject.DontDestroyOnLoad(entityGameObj)

local status, World

local URP = CS.UnityEngine.Rendering.GraphicsSettings.renderPipelineAsset ~= nil

local util = require("util")
local newVersionWorld = util.IsCSharpClass(CS.Unity.Entities.DefaultWorldInitialization)


local url_operation_mgr = require "url_operation_mgr"

local device_param_util = require "device_param_util"
local deviceLevel = device_param_util.JudgeDeviceLevel()

EntityManager.EcsDev = val.get("sw_ecs_opt1", 0) == 1
EntityManager.URP22 = URP and ( EntityManager.EcsDev and url_operation_mgr.GetConfig("enable_ecs") ~= false)

EntityManager.SandEcs = EntityManager.URP22 and EntityManager.SandEcs and (EntityManager.EcsDev and url_operation_mgr.GetConfig("enable_sand_ecs") ~= false)
EntityManager.SpriteEcs = EntityManager.SpriteEcs and EntityManager.SandEcs
EntityManager.ModelEcs = EntityManager.ModelEcs and EntityManager.SandEcs
EntityManager.FaceItemEcs = EntityManager.FaceItemEcs and EntityManager.SandEcs and util.IsCSharpClass(CS.Unity.Entities.MaterialGcSystem)

EntityManager.CysoldierssortieEcs = EntityManager.URP22 and EntityManager.CysoldierssortieEcs and (EntityManager.EcsDev and url_operation_mgr.GetConfig("enable_cysoldierssortie_ecs") ~= false)
EntityManager.HomeEcs = EntityManager.URP22 and EntityManager.HomeEcs and (EntityManager.EcsDev and url_operation_mgr.GetConfig("enable_home_ecs") ~= false)

EntityHybridUtility =  util.IsCSharpClass(CS.Unity.Entities.EntityHybridUtility) and  CS.Unity.Entities.EntityHybridUtility
EntityManager.VegetationEcs = EntityManager.VegetationEcs and EntityManager.SandEcs and url_operation_mgr.GetConfig("enable_sand_ecs_vegetation") ~= false and EntityHybridUtility and EntityHybridUtility.EnableVegetationEcs

if newVersionWorld then
    World = CS.Unity.Entities.DefaultWorldInitialization.Initialize("default", false)
    EntityManager.World = World
    EntityManager.CSVersion = World.CodeVersion or EntityManager.CSVersion


    log.Warning("EntityManager.CSVersion", EntityManager.CSVersion, "URP22(ECSBase)", EntityManager.URP22, "EcsDev", EntityManager.EcsDev, "SandEcs", EntityManager.SandEcs, "SpriteEcs", EntityManager.SpriteEcs, "ModelEcs", EntityManager.ModelEcs, "FaceItemEcs", EntityManager.FaceItemEcs, "VegetationEcs", EntityManager.VegetationEcs,
        "CysoldierssortieEcs", EntityManager.CysoldierssortieEcs, "HomeEcs", EntityManager.HomeEcs,"DeviceLevel", deviceLevel)

    if  EntityManager.VegetationEcs then
        EntityHybridUtility.EnableVegetationEcs(true)
    end


    local LuaEntityCulling = World:GetExistingSystemManaged(typeof(CS.Unity.Entities.LuaEntityCulling))
    LuaEntityCulling.Enabled = false
    local LuaEntityTransformUpdate = World:GetExistingSystemManaged(typeof(CS.Unity.Entities.LuaEntityTransformUpdate))
    LuaEntityTransformUpdate.Enabled = false
    local LuaEntityMovementSystem = World:GetExistingSystemManaged(typeof(CS.Unity.Entities.LuaEntityMovementSystem))
    LuaEntityMovementSystem.Enabled = false
    local LuaPointCloudProcessor = World:GetExistingSystemManaged(typeof(CS.Unity.Entities.LuaPointCloudProcessor))
    LuaPointCloudProcessor.Enabled = false

    if not EntityManager.URP22 then
        local SkinMeshSystem = World:GetExistingSystemManaged(typeof(CS.Unity.Entities.SkinMeshSystem))
        SkinMeshSystem.Enabled = false
    end
    
    EntityHybridUtility.SetLuaEnv(CS.War.Script.LuaEnvironment.LuaEnv)
    if EntityHybridUtility.SetPlanarShadowCullDist then
        EntityHybridUtility.SetPlanarShadowCullDist(500)
    end

else
    status, World = xpcall(CS.Unity.Entities.World, log.Warning, "default")
    EntityManager.World = World
    if World then
        CS.Unity.Entities.World.Active = World
        EntityManager.CSVersion = World.CodeVersion or EntityManager.CSVersion
 
        CS.Unity.Entities.ScriptBehaviourUpdateOrder.UpdatePlayerLoop(World)
        local PlayerLoopType = typeof(CS.Unity.Entities.PlayerLoopDisableManager)
        local PlayerLoopDisableManager = entityGameObj:AddComponent(PlayerLoopType)
        PlayerLoopDisableManager.IsActive = true
        PlayerLoopDisableManager:SetUnloadFunction(function()
            CS.Unity.Entities.World.DisposeAllWorlds()
            CS.Unity.Entities.ScriptBehaviourUpdateOrder.UpdatePlayerLoop(nil)
        end)

    else
        entityGameObj:AddComponent(typeof(CS.Entities.World))
        entityGameObj:AddComponent(typeof(CS.Entities.LuaBarrierSystem))
        --local gpuAnimatorSystem = CS.Entities.World.Instance:GetOrCreateSystem(typeof(CS.Entities.GpuAnimatorSystem))
        --local renderMeshSystem = CS.Entities.World.Instance:GetOrCreateSystem(typeof(CS.Entities.RenderMeshSystem))
        --local skinMeshSytem = CS.Entities.World.Instance:GetOrCreateSystem(typeof(CS.Entities.SkinMeshSytem))

    end
end

CS.XLua.LuaArrAccessAPI.RegisterPinFunc(CS.War.Script.LuaEnvironment.LuaEnv.L)

local LuaCSharpArr = require("lua_csharp_arr")
--local LuaCSharpArrTest = require("lua_csharp_arr_test")

local viewLodLevel = 6
local distScaleFactor = 1.0

local entitieParamTable = LuaCSharpArr.New(4)
local function CSFillEntitiesParamFunc()
    -- print("GetEntitiesCount0"..tostring(#positions))
    -- for i=1, #positions do 
    --     print(tostring(positions[i]))
    -- end

    -- print("GetEntitiesCount1")
    entitieParamTable[1] = #entities
    entitieParamTable[2] = #entities * 3
    entitieParamTable[3] = viewLodLevel
    entitieParamTable[4] = distScaleFactor
end

function EntityManager.GSetLodLevelAndScaleFactor(viewlevel, scaleFactor)
    viewLodLevel = viewlevel
    distScaleFactor = scaleFactor
end

local LuaEntityCulling

local LuaEntityTransformUpdate

local delayUnsafeRemove = {}

function EntityManager.Init()
    entities = {}
    delayUnsafeRemove = {}
    --_freeIndices = {}
    local NUM = 65
    positions = LuaCSharpArr.New(NUM * 3)
    radiuses = LuaCSharpArr.New(NUM)
    scales = LuaCSharpArr.New(NUM)
    stateMasks = LuaCSharpArr.New(NUM)
    pixelOffsets = LuaCSharpArr.New(NUM)
    lodRanges = LuaCSharpArr.New(NUM)
    parentEntities = LuaCSharpArr.New(NUM)
    entitiesIndexer = LuaCSharpArr.New(NUM)
    if EntityManager.CSVersion >= 8 then
        hybridEntities = LuaCSharpArr.New(NUM)
        rotations = LuaCSharpArr.New(NUM)
    end

    loadQueueTable = LuaCSharpArr.New(128)
    unLoadQueueTable = LuaCSharpArr.New(128)
    innerLodChangedTable = LuaCSharpArr.New(128)

    for i=1, #entitiesIndexer do
        entitiesIndexer[i] = i + 1
    end
    entitiesIndexer[#entitiesIndexer] = 0
    nextFreeEntityIndex = 1
    entitiesIndexerCount = #entitiesIndexer


    if EntityManager.CSVersion >= 6 then
        LuaEntityCulling = World:GetExistingSystemManaged(typeof(CS.Unity.Entities.LuaEntityCulling))
        LuaEntityCulling.Enabled = true
        LuaEntityTransformUpdate = World:GetExistingSystemManaged(typeof(CS.Unity.Entities.LuaEntityTransformUpdate))
        LuaEntityTransformUpdate.Enabled = true

        if transformAccessArray and transformAccessArray.isCreated then
            transformAccessArray:Dispose()
            transformAccessArray = nil
        end
    else
        if World then
            LuaEntityCulling = World:GetOrCreateSystem(typeof(CS.Unity.Entities.LuaEntityCulling))
            World:AddSystemToGroup(LuaEntityCulling)
            LuaEntityTransformUpdate = World:GetOrCreateSystem(typeof(CS.Unity.Entities.LuaEntityTransformUpdate))
            World:AddSystemToGroup(LuaEntityTransformUpdate)
        else
            LuaEntityCulling = CS.Entities.World.Instance:GetOrCreateSystem(typeof(CS.Entities.LuaEntityCulling))
            LuaEntityTransformUpdate = CS.Entities.World.Instance:GetOrCreateSystem(typeof(CS.Entities.LuaEntityTransformUpdate))
        end
    end

    LuaEntityCulling:PinLuaTable(positions:GetCSharpAccess(), scales:GetCSharpAccess(), radiuses:GetCSharpAccess(), lodRanges:GetCSharpAccess(), stateMasks:GetCSharpAccess(), parentEntities:GetCSharpAccess(), entitiesIndexer:GetCSharpAccess(),
            CSFillEntitiesParamFunc, loadQueueTable:GetCSharpAccess(), unLoadQueueTable:GetCSharpAccess(), innerLodChangedTable:GetCSharpAccess(), entitieParamTable:GetCSharpAccess())

    transformAccessArray = CS.UnityEngine.Jobs.TransformAccessArray(128)
    if EntityManager.CSVersion >= 8 then
        LuaEntityTransformUpdate:PinLuaTable(positions:GetCSharpAccess(), stateMasks:GetCSharpAccess(), scales:GetCSharpAccess(), pixelOffsets:GetCSharpAccess(), parentEntities:GetCSharpAccess(), entitiesIndexer:GetCSharpAccess(),hybridEntities:GetCSharpAccess(), rotations:GetCSharpAccess(),transformAccessArray)
    else
        LuaEntityTransformUpdate:PinLuaTable(positions:GetCSharpAccess(), stateMasks:GetCSharpAccess(), scales:GetCSharpAccess(), pixelOffsets:GetCSharpAccess(), parentEntities:GetCSharpAccess(), entitiesIndexer:GetCSharpAccess(), transformAccessArray)
    end
    
end

function EntityManager.Clear()
    if EntityManager.CSVersion >= 6 then
        World:GetExistingSystemManaged(typeof(CS.Unity.Entities.PreUpdateBarrierSystem)):SyncJob()
        LuaEntityCulling.Enabled = false
        LuaEntityCulling = nil
        LuaEntityTransformUpdate.Enabled = false
        LuaEntityTransformUpdate = nil
    else  
        if World then
            World:GetExistingSystem(typeof(CS.Unity.Entities.PreUpdateBarrierSystem)):SyncJob()
            if LuaEntityCulling then
                World:DestroySystem(LuaEntityCulling)
                LuaEntityCulling = nil
            end
            if LuaEntityTransformUpdate then
                World:DestroySystem(LuaEntityTransformUpdate)
                LuaEntityTransformUpdate = nil
            end
        else
            if LuaEntityCulling then
                CS.Entities.World.Instance:RemoveSystem(LuaEntityCulling)
                LuaEntityCulling = nil
            end
            if LuaEntityTransformUpdate then
                CS.Entities.World.Instance:RemoveSystem(LuaEntityTransformUpdate)
                LuaEntityTransformUpdate = nil
            end

        end
    end

    entities = nil
    delayUnsafeRemove = {}
    --_freeIndices = nil
    nextFreeEntityIndex = 0
    positions = nil
    radiuses = nil
    scales = nil
    stateMasks = nil
    pixelOffsets = nil
    lodRanges = nil
    parentEntities = nil
    if EntityManager.CSVersion >= 8 then
        hybridEntities = nil
    end
    entitiesIndexer = nil
    entitiesIndexerCount = 0

    loadQueueTable = nil
    unLoadQueueTable = nil
    innerLodChangedTable = nil

    -- if transformAccessArray and transformAccessArray.isCreated then
    --     transformAccessArray:Dispose()
    --     transformAccessArray = nil
    -- end

end

local placeholderGameObj = CS.UnityEngine.GameObject("placeholder")
local placeholderTransform = placeholderGameObj.transform
CS.UnityEngine.GameObject.DontDestroyOnLoad(placeholderGameObj)
local SetTransformAccess = CS.ArrayExtensions.SetTransformAccess

function EntityManager.GSetHudWorldSpace(hudcamera, hudCanvas)
    if EntityManager.CSVersion >= 6 then
        LuaEntityTransformUpdate = World:GetExistingSystemManaged(typeof(CS.Unity.Entities.LuaEntityTransformUpdate))
    else
        if World then
            LuaEntityTransformUpdate = World:GetOrCreateSystem(typeof(CS.Unity.Entities.LuaEntityTransformUpdate))
            World:AddSystemToGroup(LuaEntityTransformUpdate)
        else
            LuaEntityTransformUpdate = CS.Entities.World.Instance:GetOrCreateSystem(typeof(CS.Entities.LuaEntityTransformUpdate))
        end
    end
    LuaEntityTransformUpdate:HudCanvasToWorldSpace(hudcamera, hudCanvas)
    hudcamera.cullingMask = 32 -- GROUND -> UI Only
end

function EntityManager.GGetPositions()
    return positions
end

function EntityManager.GGetStateMasks()
    return stateMasks
end

function EntityManager.GGetEntitiesIndexer()
    return entitiesIndexer
end

--------------------------------------------entity_indexer----------------------------------------------------------------
local function IsValid(entity)
    if entitiesIndexerCount < entity.index then
        return false
    end
    return floor(entitiesIndexer[entity.index] / 100000) == entity.version
end

local function IsValidEntityID(entityID)
    local entityIndex = entityID % 100000
    local version = floor(entityID / 100000)

    if entitiesIndexerCount < entityIndex or entityIndex <=0 then
        return false
    end
    return floor(entitiesIndexer[entityIndex] / 100000) == version, entityIndex
end

local function CreateEntity(arrayIndex)
    -- Reuse
    if nextFreeEntityIndex > 0 then
        local entityIndex = nextFreeEntityIndex
        nextFreeEntityIndex = entitiesIndexer[entityIndex] % 100000
        -- local item = _entitiesIndexer[entityIndex]
        -- local newVersion = item.version + 1
        -- item.version = newVersion
        -- item.arrayIndex = arrayIndex
        -- local newVersion = _entitiesIndexer[entityIndex].version + 1

        -- _entitiesIndexer[entityIndex] = {
        --     arrayIndex = arrayIndex,
        --     version = newVersion,
        -- }
        local newVersion = floor(entitiesIndexer[entityIndex] / 100000) + 1
        entitiesIndexer[entityIndex] = arrayIndex + newVersion * 100000
        return { index = entityIndex, version = newVersion }
    end

    -- Create new one
    local entityIndex = entitiesIndexerCount + 1
    local version = 1

    -- _entitiesIndexer[entityIndex] = {
    --     arrayIndex = arrayIndex,
    --     version = version,
    -- }

    entitiesIndexer[entityIndex] = arrayIndex + version * 100000
    entitiesIndexerCount = entityIndex
    return { index = entityIndex, version = version }
end

-- local function DestroyEntity(entity)
--     table.insert(_freeIndices, entity.index)

--     _entitiesIndexer[entity.index].version = item.version + 1
-- end

-- local function GetItem(entity)
--     return _entitiesIndexer[entity.index]
-- end

-- local function UpdateIndex(entity, newArrayIndex)
--     _entitiesIndexer[entity.index].arrayIndex = newArrayIndex
-- end

-------------------------------------------------Mask --------------------------------------------------
---todo Slower due to call overhead 升级到lua5.4 or luau 使用Bitwise Operators & | ~
local lshift = bit.lshift
local bnot = bit.bnot
local bor = bit.bor
local band = bit.band
local bxor = bit.bxor

-- enum StateMasks
-- {
--     Active = 1,
--     Loaded = 2,
--     Loading = 4,
-- }
local ActiveMask = lshift(1, 0)
local NotActiveMask = bnot(ActiveMask)
local LoadedMask = lshift(1, 1)
local NotLoadedMask = bnot(LoadedMask)

local LoadingMask = lshift(1, 2)
local NotLoadingMask = bnot(LoadingMask)

local TransformDirtyMask = lshift(1, 3)

local HubMask = lshift(1, 4)

local CanvasMask = lshift(1, 5)

local InLodRangeMask = lshift(1, 6)

local InInnerLodMask = lshift(1, 7)

local ActiveDirtyMask = lshift(1, 0)

local HaveParentMask = lshift(1, 10)

local LogicOnlyMask = lshift(1, 20)

local HybridEntityMask = lshift(1,8)


-- local  FrustumInMask = lshift(1,1)
-- local  FrustumOutMask = bnot(FrustumInMask)

--------------------------------------------Encode number-------------------------------------------
local function EncodePixelOffsets(x, y)
    return x + 400 + (400 + y) / 1000
end

-- minLod< innerLod < maxLod
local function EncodeLodRange(minLod, maxLod, minInnerLod, maxInnerLod)
    return minLod + maxLod * 10 + (minInnerLod or 0) * 100 + (maxInnerLod or 0) * 1000
end
--------------------------------------------EntityManager-------------------------------------------
function EntityManager.AddEntity(sandEntity, bhub, bcanvas, radius)
    if not entities then
        return
    end
    --count = count + 1
    local entityIndex = #entities + 1
    local entity = CreateEntity(entityIndex)

    --add entity 1
    entities[entityIndex] = entity

    --add positions 2
    positions[entityIndex * 3 - 2] = 0
    positions[entityIndex * 3 - 1] = 0
    positions[entityIndex * 3] = 0

    --add radiuses 3
    radiuses[entityIndex] = radius or 1
    -- add stateMasks 4
    stateMasks[entityIndex] = ActiveMask + (bhub and HubMask or 0) + (bcanvas and CanvasMask or 0)

    --add scale 5
    scales[entityIndex] = 1

    --add pixelOffsets 6
    pixelOffsets[entityIndex] = 0

    -- add sandEntityCache 7
    entityObjCache[entityIndex] = sandEntity

    -- add lodRanges 8
    lodRanges[entityIndex] = EncodeLodRange(1, 6)


    -- add target/parent entity index 9
    parentEntities[entityIndex] = 0

    if EntityManager.CSVersion >= 8 then
        hybridEntities[entityIndex] = 0
        rotations[entityIndex] = 0
    end
    
    transformAccessArray:Add(placeholderTransform)

    return entity,entityIndex
end

local function ArrayHashCheck(array, length, recoveryValue)
    if #array < length then
        for i = 1, length do
            if not array[i] then
                array[i] = recoveryValue
                log.Error("ArrayHashCheck Error" .. array .. length)
            end
        end
    end
end

function EntityManager.ArrayHashCheck()
    if not entities then
        return
    end
    local length = #entities

    ArrayHashCheck(positions, length * 3, 0)
    ArrayHashCheck(radiuses, length, 1)
    ArrayHashCheck(stateMasks, length, 0)
    ArrayHashCheck(scales, length, 1)
    ArrayHashCheck(pixelOffsets, length, 0)
    ArrayHashCheck(lodRanges, length, EncodeLodRange(1, 6))
    ArrayHashCheck(parentEntities, length, 0)

    if EntityManager.CSVersion >= 8 then
        ArrayHashCheck(hybridEntities, length, 0) 
    end
    
    --transformAccessArray

end

local Time = CS.UnityEngine.Time
local SyncFrameCount = 0
function EntityManager.GSetSyncFrameCount(frameCount)
    SyncFrameCount = frameCount
end

function EntityManager.RemoveEntity(entity, noCheck)
    if not noCheck and Time.frameCount > SyncFrameCount then
        delayUnsafeRemove[#delayUnsafeRemove + 1] = entity
        return
    end
    --print("EntityManager.RemoveEntity".. tostring(#entities))
    if not IsValid(entity) then
        log.Error("invalid entity at remove entity")
        return false
    end
    local count = #entities
    --Get remap Item
    local item = entitiesIndexer[entity.index]

    local arrayIndex = item % 100000 --item.arrayIndex

    local version = floor(item / 100000)
    --DestroyEntity entity
    --_freeIndices[#_freeIndices + 1] = entity.index --table.insert(_freeIndices, entity.index)
    entitiesIndexer[entity.index] = nextFreeEntityIndex + (version + 1) * 100000  -- entitiesIndexer[entity.index] = item.version + 1
    nextFreeEntityIndex = entity.index

    if arrayIndex ~= count then
        --UpdateIndex(entities[count], arrayIndex)
        item = entitiesIndexer[entities[count].index]
        entitiesIndexer[entities[count].index] = item + arrayIndex - item % 100000
        --+ floor(entitiesIndexer[entities[count].index] / 100000) * 
    end

    --RemoveAtSwapBack entity 1
    entities[arrayIndex] = entities[count]
    --table.remove(entities)
    entities[count] = nil

    --RemoveAtSwapBack position 2
    positions[arrayIndex * 3 - 2] = positions[count * 3 - 2]
    --positions[count * 3 - 2] = nil
    positions[arrayIndex * 3 - 1] = positions[count * 3 - 1]
    --positions[count * 3 - 1] = nil
    positions[arrayIndex * 3] = positions[count * 3]
    --positions[count * 3] = nil
    -- table.remove(positions)
    -- table.remove(positions)
    -- table.remove(positions)

    --RemoveAtSwapBack radius 3
    radiuses[arrayIndex] = radiuses[count]
    --radiuses[count] = nil

    --RemoveAtSwapBack stateMask 4
    stateMasks[arrayIndex] = stateMasks[count]
    --stateMasks[count] = nil
    --table.remove( stateMasks)

    --RemoveAtSwapBack sacle 5
    scales[arrayIndex] = scales[count]
    --scales[count] = nil


    --RemoveAtSwapBack pixelOffsets 6
    pixelOffsets[arrayIndex] = pixelOffsets[count]
    --pixelOffsets[count] = nil

    --RemoveAtSwapBack sandEntityCache 7
    entityObjCache[arrayIndex] = entityObjCache[count]
    entityObjCache[count] = nil
    --count = count - 1

    --RemoveAtSwapBack lodRanges 8
    lodRanges[arrayIndex] = lodRanges[count]
    --lodRanges[count] = nil

    --RemoveAtSwapBack target/parent entity index 9
    parentEntities[arrayIndex] = parentEntities[count]
    --parentEntities[count] = nil

    if EntityManager.CSVersion >= 8 then
        hybridEntities[arrayIndex] = hybridEntities[count]
        --hybridEntities[count] = nil
        rotations[arrayIndex] = rotations[count]
        --rotations[count] = nil
    end

    transformAccessArray:RemoveAtSwapBack(arrayIndex - 1)
end

function EntityManager.SetPosition(entity, pos)
    if not entity or not pos then
        return
    end
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    positions[arrayIndex * 3 - 2] = pos.x
    positions[arrayIndex * 3 - 1] = pos.y
    positions[arrayIndex * 3] = pos.z or 0

    stateMasks[arrayIndex] = bor(stateMasks[arrayIndex], TransformDirtyMask)
end

function EntityManager.IsValid(entity)
    return IsValid(entity)
end

local EntityNull = CS.Unity.Entities.Entity.Null
function EntityManager.IsHybridValid(hybridEntity)
    return hybridEntity and hybridEntity ~=  EntityNull
end
function EntityManager.GetPosition(entity)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    return positions[arrayIndex * 3 - 2], positions[arrayIndex * 3 - 1], positions[arrayIndex * 3]
end

function EntityManager.SetRadius(entity, radius)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex

    if EntityManager.VegetationEcs then
        local  r =  radiuses[arrayIndex]
        radiuses[arrayIndex] = r + radius - r % 100000
    else
        radiuses[arrayIndex] = radius or 1
    end

    stateMasks[arrayIndex] = bor(stateMasks[arrayIndex], TransformDirtyMask)
end

function EntityManager.SetColliderExtent(entity, extent)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    if not extent  then
        return
    end
    if EntityManager.VegetationEcs then
        radiuses[arrayIndex] = radiuses[arrayIndex] % 100000 + extent * 100000
    end
    --stateMasks[arrayIndex] = bor(stateMasks[arrayIndex], TransformDirtyMask)
end

function EntityManager.SetLodRange(entity, minLod, maxLod, minInnerLod, maxInnerLod)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    lodRanges[arrayIndex] = EncodeLodRange(minLod, maxLod, minInnerLod, maxInnerLod)
end

function EntityManager.GetRadius(entity)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    return radiuses[arrayIndex] % 100000
end

function EntityManager.SetPositionXYZ(entity, x, y, z)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    positions[arrayIndex * 3 - 2] = x
    positions[arrayIndex * 3 - 1] = y
    positions[arrayIndex * 3] = z or 0

    stateMasks[arrayIndex] = bor(stateMasks[arrayIndex], TransformDirtyMask)

    -- local log = require "log"
    -- log.Error("SetPosition entity.index " .. entity.index .. "arrayIndex" .. arrayIndex .. "x z" .. x.. " " .. z)

    -- for i=1, #parentEntities do
    --     if parentEntities[i] % 100000 == entity.index then
    --         log.Error("found child at arrayIndex" ..i)
    --     end
    -- end
end

function EntityManager.SetParent(entity, targetEntity)
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    parentEntities[arrayIndex] = targetEntity.index + targetEntity.version * 100000
    stateMasks[arrayIndex] = bor(stateMasks[arrayIndex], HaveParentMask)

    -- local log = require "log"
    -- log.Error("Set arrayIndex  ".. arrayIndex .. "parent entity.index " .. targetEntity.index.. "parent arrayIndex" .. entitiesIndexer[targetEntity.index] % 100000) 

end

function EntityManager.SetHybridEntity(entity,targetEntity)
    local attachTrans = EntityHybridUtility.GetRootTransform(targetEntity)
    if attachTrans then
        return attachTrans
    end
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    local entityID = EnityConversionUtility.EncodeEntityId(targetEntity)
    hybridEntities[arrayIndex] = entityID --targetEntity.Index + targetEntity.Version * 100000
    stateMasks[arrayIndex] = bor(stateMasks[arrayIndex], TransformDirtyMask + HybridEntityMask)

    if EntityHybridUtility.SetSpriteBillBord then
        EntityHybridUtility.SetSpriteBillBord(targetEntity, pixelOffsets[arrayIndex])
    else
        log.Error("EntityHybridUtility.SetSpriteBillBord not found")
    end
end


function EntityManager.SetRotation(entity, pitch, yaw,  roll)
    if not rotations then
        return
    end
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    rotations[arrayIndex] = yaw -- Encode rotation as a single number

    stateMasks[arrayIndex] = bor(stateMasks[arrayIndex], TransformDirtyMask)
end
--Lua doubles (64-bit) can represent exact integers up to 2^53 -1 which is approximately 9.007 x 10^15

function EntityManager.SetHudOffsetAndScale(entity, screen_offset_level, screen_offset_x, screen_offset_y, target_offset_x, target_offset_y, needScale, needOffsetScale)
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    local sign_screen_offset_level_x = 0 -- = screen_offset_x >= 0 and 0 or 1
    if screen_offset_x < 0 then
        screen_offset_x = -screen_offset_x
        sign_screen_offset_level_x = 1
    end

    local sign_screen_offset_level_y = 0 --screen_offset_y >= 0 and 0 or 2
    if screen_offset_y < 0 then
        screen_offset_y = -screen_offset_y
        sign_screen_offset_level_y = 2
    end

    local sign_target_offset_x = 0 --target_offset_x >= 0 and 0 or 4
    if target_offset_x < 0 then
        target_offset_x = -target_offset_x
        sign_target_offset_x = 4
    end

    local sign_target_offset_y = 0 --target_offset_y >= 0 and 0 or 8
    if target_offset_y < 0 then
        target_offset_y = -target_offset_y
        sign_target_offset_y = 8
    end

    local needScale_value = needScale and 16 or 0
    local needOffsetScale_value = needOffsetScale and 32 or 0
    local screen_offset_level_value = screen_offset_level * 64

    local encode = floor(screen_offset_x) * 1024 --2^10
            + floor(screen_offset_y) * 1048576 --2^20
            + floor(target_offset_x * 10) * 1073741824 -- 2^30
            + floor(target_offset_y * 10) * 1099511627776 --2^40
            + sign_screen_offset_level_x
            + sign_screen_offset_level_y
            + sign_target_offset_x
            + sign_target_offset_y
            + needScale_value
            + needOffsetScale_value
            + screen_offset_level_value


    local stateMask = stateMasks[arrayIndex]
    if band(stateMask ,CanvasMask) ~= 0 then
        encode = encode + 2251799813685248 -- 2^51
    end
    if band(stateMask,HaveParentMask) ~= 0 then
        encode = encode + 4503599627370496 -- 2^52
    end


    pixelOffsets[arrayIndex] = encode
end

function EntityManager.SetActive(entity, enable)
    if not entity then
        return
    end
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    local mask = stateMasks[arrayIndex]
    if enable then
        mask = bor(mask, ActiveMask + ActiveDirtyMask)
    else
        mask = band(mask, NotActiveMask)
        mask = bor(mask, ActiveDirtyMask)
    end
    stateMasks[arrayIndex] = mask
end

function EntityManager.SetScale(entity, scale)
    if not entity then
        return
    end
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    scales[arrayIndex] = scale
    stateMasks[arrayIndex] = bor(stateMasks[arrayIndex], TransformDirtyMask)
end

function EntityManager.SetLoaded(entity, transform)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    local mask = stateMasks[arrayIndex]
    mask = bor(mask, LoadedMask + TransformDirtyMask)
    mask = band(mask, NotLoadingMask)
    stateMasks[arrayIndex] = mask

    if transform then
        SetTransformAccess(transformAccessArray, arrayIndex - 1, transform)
    end
end

function EntityManager.SetLogicOnlyMask(entity)
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    local mask = stateMasks[arrayIndex]
    mask = bor(mask, LoadedMask + ActiveMask + LogicOnlyMask)
    stateMasks[arrayIndex] = mask
end

function EntityManager.SetUnLoaded(entity)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    local mask = stateMasks[arrayIndex]
    mask = band(mask, NotLoadedMask)
    mask = band(mask, NotLoadingMask)
    stateMasks[arrayIndex] = mask
    
    if hybridEntities then
        hybridEntities[arrayIndex] = 0
    end
    
    SetTransformAccess(transformAccessArray, arrayIndex - 1, placeholderTransform)
end

function EntityManager.SetLoading(entity)
    --local item = entitiesIndexer[entity.index]
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    local mask = stateMasks[arrayIndex]
    stateMasks[arrayIndex] = bor(mask, LoadingMask)
end

function EntityManager.SetStateMask(entity, mask, on)
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    local oldMask = stateMasks[arrayIndex]
    if on then
        stateMasks[arrayIndex] = bor(oldMask, mask)
    else
        stateMasks[arrayIndex] = band(oldMask, bnot(mask))
    end
end

function EntityManager.HasStateMask(entity, mask)
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    return band(stateMasks[arrayIndex], mask) ~= 0
end

function EntityManager.GetEntityObject(entity)
    local arrayIndex = entitiesIndexer[entity.index] % 100000--item.arrayIndex
    return entityObjCache[arrayIndex]
end

function EntityManager.GetEntityObjectByNumber(entityID)
    local valid, entityIndex = IsValidEntityID(entityID)
    if not valid then
        return nil
    end
    local arrayIndex = entitiesIndexer[entityIndex] % 100000--item.arrayIndex
    return entityObjCache[arrayIndex]
end

function EntityManager.GetLuaEntityTransformCanvasHeight()
  return LuaEntityTransformUpdate and LuaEntityTransformUpdate.canvasHeight
end

--这里更新时机要早防止EntityManager.RemoveEntity造成 arrayIndex 改变
local UnloadEntityObjs = setmetatable({}, { __mode = "v" })
function EntityManager.Update()
    EntityManager.ArrayHashCheck()
    --print("EntityManager.Update")
    local reqLoadCount = min(loadQueueTable[1], 40)
    local reqUnloadCount = min(unLoadQueueTable[1],40)
    local innerLodChangedCount = min(innerLodChangedTable[1],20)

    if not EntityManager.On then
        return
    end

    for i = 1, innerLodChangedCount do
        local arrayIndex = innerLodChangedTable[i + 1]
        local stateMask = stateMasks[arrayIndex]
        local entityObj = entityObjCache[arrayIndex]
        if entityObj then
            local status = xpcall(entityObj.OnInnerVisible, log.Error, entityObj, band(stateMask, InInnerLodMask) == 0)
            --entityObjCache[arrayIndex]:OnInnerVisible(band(stateMask, InInnerLodMask) == 0)
            stateMasks[arrayIndex] = bxor(stateMask, InInnerLodMask)
        else
            log.Error("innerLodChangedTable Error " .. arrayIndex)
        end
    end

    if EntityManager.Streaming then
        if reqLoadCount > 0 then
            --log.Warning("Entities: lua loadQueueTable "..reqLoadCount)
        end
        for i = 1, reqLoadCount do
            --print(tostring(i) .. " " .. loadQueueTable[i + 1] )
            local arrayIndex = loadQueueTable[i + 1]
            local entityObj = entityObjCache[arrayIndex]
            if entityObj then
                --entityObjCache[arrayIndex]:_InstantiateModelAsync()
                local status = xpcall(entityObj._InstantiateModelAsync, log.Error, entityObj)
            else
                log.Error("loadQueueTable Error" .. arrayIndex)
            end
        end
    end
    loadQueueTable[1] = 0

    -- print("lua unLoadQueueTable"..reqUnloadCount)
    if EntityManager.StreamingUnload then
        if reqUnloadCount > 0 then
            --log.Warning("Entities: lua unLoadQueueTable "..reqUnloadCount)
        end

        for i = 1, reqUnloadCount do
            --print(tostring(i) .. " " ..  unLoadQueueTable[i + 1] )
            local arrayIndex = unLoadQueueTable[i + 1]
            UnloadEntityObjs[i] = entityObjCache[arrayIndex]
        end

        local v
        for i = 1, reqUnloadCount do
            v = UnloadEntityObjs[i]
            if v then
                xpcall(v.Dispose, log.Error, v, true)
            end
        end
        
    end

    unLoadQueueTable[1] = 0

    if #delayUnsafeRemove > 0 then
        for i, v in ipairs(delayUnsafeRemove) do
            EntityManager.RemoveEntity(v, true)
        end
        delayUnsafeRemove = {}
    end

end

return EntityManager
